package com.frt.usercore.manager.impl;

import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.frt.usercore.common.constants.CommonConstant;
import com.frt.usercore.common.enums.exception.base.ErrorBusinessTypeEnum;
import com.frt.usercore.common.enums.exception.base.ErrorCodeEnum;
import com.frt.usercore.common.exception.InternalException;
import com.frt.usercore.common.utils.IdWorkerUtil;
import com.frt.usercore.common.utils.LogUtil;
import com.frt.usercore.dao.entity.TenantSmsChannelConfigsDO;
import com.frt.usercore.dao.entity.TenantSmsTemplatesDO;
import com.frt.usercore.domain.param.common.SmsMessageParam;
import com.frt.usercore.manager.SmsManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 专信云短信管理实现类
 */
@Slf4j
@Service
public class ZhuanXinYunSmsManagerImpl implements SmsManager {

	private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

	static {
		OBJECT_MAPPER.setSerializationInclusion(JsonInclude.Include.NON_NULL);
		OBJECT_MAPPER.setPropertyNamingStrategy(PropertyNamingStrategy.SNAKE_CASE);
		// 配置当对象没有字段时不抛出异常，解决 "Class has no fields" 错误
		OBJECT_MAPPER.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
	}

	/**
	 * 发送消息
	 *
	 * @param param 短信参数
	 */
	@Override
	public void sendMessage(SmsMessageParam param) {
		TenantSmsChannelConfigsDO smsChannelConfigsDO = param.getChannelConfig();
		LogUtil.info(log, "ZhuanXinYunSmsManagerImpl.sendZhuanXinYunSms >> 专信云渠道发送短信开始 >> param = {}", JSON.toJSONString(param));

		try {
			// 构建专信云短信内容
			String smsContent = buildZhuanXinYunSmsContent(param);

			// 构建请求参数
			String missionNums = IdWorkerUtil.getSingleId();
			Map<String, Object> paramMap = new HashMap<>();
			paramMap.put("appKey", smsChannelConfigsDO.getAccessKeyId());
			paramMap.put("appSecret", smsChannelConfigsDO.getAccessKeySecret());
			paramMap.put("phones", param.getPhoneNumber());
			paramMap.put("content", smsContent);
			paramMap.put("missionNums", missionNums);

			LogUtil.info(log, "ZhuanXinYunSmsManagerImpl.sendZhuanXinYunSms >> 专信云渠道发送短信请求参数 >> paramMap = {}", JSONUtil.toJsonStr(paramMap));

			// 发送HTTP请求
			String resultStr = HttpUtil.post(CommonConstant.ZHAN_XIN_YUN_SEND_SMS_URL, paramMap);
			LogUtil.info(log, "ZhuanXinYunSmsManagerImpl.sendZhuanXinYunSms >> 专信云渠道发送短信响应 >> resultStr = {}", resultStr);

			// 处理响应结果
			handleZhuanXinYunSmsResponse(resultStr, param);

		} catch (Exception e) {
			LogUtil.error(log, "ZhuanXinYunSmsManagerImpl.sendZhuanXinYunSms >> 专信云渠道发送短信异常 >> param = {}", param, e);
			throw new InternalException(ErrorCodeEnum.REQUEST_APPLY_ERROR.getErrorCode(), ErrorBusinessTypeEnum.EXTERNAL_ERROR.getBusinessType(), "专信云渠道发送短信异常");
		}
	}

	/**
	 * 构建专信云短信内容
	 * 使用模板内容替换参数，生成最终的短信内容
	 *
	 * @param param 短信发送参数
	 * @return 构建后的短信内容（包含签名）
	 */
	private String buildZhuanXinYunSmsContent(SmsMessageParam param) {
		LogUtil.info(log, "ZhuanXinYunSmsManagerImpl.buildZhuanXinYunSmsContent >> 构建专信云短信内容开始 >> param = {}", JSON.toJSONString(param));
		TenantSmsTemplatesDO smsTemplate = param.getSmsTemplate();
		// 获取签名
		String sign = CommonConstant.SMS_SIGN_START + smsTemplate.getSignatureCode() + CommonConstant.SMS_SIGN_END;
		// 获取模板内容
		String templateContent = smsTemplate.getTemplateContent();
		if (StringUtils.isBlank(templateContent)) {
			LogUtil.error(log, "ZhuanXinYunSmsManagerImpl.buildZhuanXinYunSmsContent >> 模板内容为空 >> templateId = {}", smsTemplate.getId());
			throw new InternalException(ErrorCodeEnum.PARAMETER_VALIDATION_FAILED.getErrorCode(), ErrorBusinessTypeEnum.EXTERNAL_ERROR.getBusinessType(), "短信模板内容不能为空");
		}

		// 替换模板参数
		String content = replaceTemplateParams(templateContent, param.getParam());

		// 拼接签名和内容
		String smsContent = sign + content;

		LogUtil.info(log, "ZhuanXinYunSmsManagerImpl.buildZhuanXinYunSmsContent >> 构建专信云短信内容完成 >> smsContent = {}", smsContent);
		return smsContent;
	}

	/**
	 * 处理专信云短信发送响应
	 *
	 * @param resultStr 响应字符串
	 * @param param     原始请求参数
	 */
	private void handleZhuanXinYunSmsResponse(String resultStr, SmsMessageParam param) {
		if (StringUtils.isBlank(resultStr)) {
			LogUtil.error(log, "ZhuanXinYunSmsManagerImpl.handleZhuanXinYunSmsResponse >> 专信云短信发送响应为空 >> param = {}", param);
			throw new InternalException(ErrorCodeEnum.REQUEST_APPLY_ERROR.getErrorCode(), ErrorBusinessTypeEnum.EXTERNAL_ERROR.getBusinessType(), "专信云短信发送响应为空");
		}

		try {
			JSONObject resultObject = JSON.parseObject(resultStr);
			String errorCode = resultObject.getString("errorCode");

			if (!CommonConstant.ZHAN_XIN_YUN_SUCCESS_CODE.equals(errorCode)) {
				String errorMsg = resultObject.getString("errorMsg");
				LogUtil.error(log, "ZhuanXinYunSmsManagerImpl.handleZhuanXinYunSmsResponse >> 专信云短信发送失败 >> errorCode = {}, errorMsg = {}, param = {}",
						errorCode, errorMsg, param);
				throw new InternalException(ErrorCodeEnum.REQUEST_APPLY_ERROR.getErrorCode(), ErrorBusinessTypeEnum.EXTERNAL_ERROR.getBusinessType(), "专信云短信发送失败：" + errorMsg);
			}

			LogUtil.info(log, "ZhuanXinYunSmsManagerImpl.handleZhuanXinYunSmsResponse >> 专信云短信发送成功 >> param = {}", param);

		} catch (Exception e) {
			LogUtil.error(log, "ZhuanXinYunSmsManagerImpl.handleZhuanXinYunSmsResponse >> 解析专信云响应异常 >> resultStr = {}, param = {}", resultStr, param, e);
			throw new InternalException(ErrorCodeEnum.REQUEST_APPLY_ERROR.getErrorCode(), ErrorBusinessTypeEnum.EXTERNAL_ERROR.getBusinessType(), "解析专信云响应失败");
		}
	}

	/**
	 * 替换模板参数
	 * 将模板中的${param}占位符替换为实际参数值
	 *
	 * @param templateContent 模板内容
	 * @param templateParam   模板参数
	 * @return 替换后的内容
	 */
	private String replaceTemplateParams(String templateContent, Object templateParam) {
		if (templateParam == null) {
			return templateContent;
		}

		try {
			// 将参数对象转换为Map
			String paramJson = OBJECT_MAPPER.writeValueAsString(templateParam);
			Map<String, Object> paramMap = JSON.parseObject(paramJson, new TypeReference<Map<String, Object>>() {});

			String result = templateContent;
			// 替换模板中的占位符
			for (Map.Entry<String, Object> entry : paramMap.entrySet()) {
				String placeholder = "${" + entry.getKey() + "}";
				String value = entry.getValue() != null ? entry.getValue().toString() : "";
				result = result.replace(placeholder, value);
			}

			return result;
		} catch (Exception e) {
			LogUtil.error(log, "ZhuanXinYunSmsManagerImpl.replaceTemplateParams >> 替换模板参数异常 >> templateContent = {}, templateParam = {}", templateContent, templateParam, e);
			throw new InternalException(ErrorCodeEnum.REQUEST_APPLY_ERROR.getErrorCode(), ErrorBusinessTypeEnum.EXTERNAL_ERROR.getBusinessType(), "模板参数替换失败");
		}
	}

}
