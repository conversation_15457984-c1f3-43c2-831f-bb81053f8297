package com.frt.usercore.common.enums.business;

import lombok.Getter;

/**
 * 角色类型枚举
 *
 * <AUTHOR>
 * @since 2025-08-28
 */
@Getter
public enum RoleTypeEnum {

    /**
     * 运营
     */
    OPERATION(1, "运营"),

    /**
     * 代理商
     */
    AGENT(2, "代理商"),

    /**
     * 商户
     */
    MERCHANT(3, "商户");

    private final Integer code;
    private final String description;

    RoleTypeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }
}
