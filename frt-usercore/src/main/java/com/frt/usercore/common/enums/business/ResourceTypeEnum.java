package com.frt.usercore.common.enums.business;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0 ResourceTypeEnum
 * @date 2025/8/29 10:55
 */
@Getter
public enum ResourceTypeEnum {

    /**
     * 后台二级域名
     */
    DOMAIN_NAME(1, "后台二级域名"),

    /**
     *
     */
    MINA_REMARK(2, "小程序标识"),
    /**
     *
     */
    BACK_GROUND(3, "背景图"),

    /**
     * logo
     */
    LOGO(3, "logo"),

    /**
     * 主题色
     */
    THEME_COLOR(4,"主题色");

    private final Integer code;
    private final String description;

    ResourceTypeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }
}
