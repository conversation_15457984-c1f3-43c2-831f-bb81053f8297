package com.frt.usercore.common.enums.business;

import lombok.Getter;

/**
 * 人员类型枚举
 */
@Getter
public enum UserTypeEnum {

    /**
     * 运营后台超管
     */
    OPERATION_SUPER_ADMIN(1, "运营后台超管"),

    /**
     * 运营后台员工
     */
    OPERATION_STAFF(2, "运营后台员工"),

    /**
     * 代理商超管
     */
    AGENT_SUPER_ADMIN(3, "代理商超管"),

    /**
     * 代理商员工
     */
    AGENT_STAFF(4, "代理商员工"),

    /**
     * 商户超管
     */
    MERCHANT_SUPER_ADMIN(5, "商户超管"),

    /**
     * 商户员工
     */
    MERCHANT_STAFF(6, "商户员工");

    private final Integer code;
    private final String description;

    UserTypeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }
}