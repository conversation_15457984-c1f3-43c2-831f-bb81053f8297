package com.frt.usercore.domain.param.storemanager;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;

@Data
public class StoreInfoAddParam {
    /**
     * 商家id
     */
    @NotBlank(message = "商户ID不能为空")
    private String merchantId;
    /**
     * 门店名称
     */
    @NotBlank(message = "门店名称不能为空")
    private String storeName;
    /**
     * 省
     */
    @NotBlank(message = "省不能为空")
    private String provinceName;
    /**
     * 省code
     */
    @NotBlank(message = "省不能为空")
    private String provinceCode;
    /**
     * 市
     */
    @NotBlank(message = "市不能为空")
    private String cityName;
    /**
     * 市code
     */
    @NotBlank(message = "市不能为空")
    private String cityCode;
    /**
     * 区
     */
    @NotBlank(message = "区不能为空")
    private String countyName;
    /**
     * 区code
     */
    @NotBlank(message = "区不能为空")
    private String countyCode;
    /**
     * 地址
     */
    @NotBlank(message = "地址不能为空")
    private String storeAddress;
    /**
     * 门店电话
     */
    @NotBlank(message = "门店电话不能为空")
    private String storePhone;
    /**
     * 行业类目
     */
    @NotEmpty(message = "行业类目不能为空")
    private Integer unityCatId;
    /**
     * 门头照
     */
    @NotEmpty(message = "门头照不能为空")
    private String storeDoorPic;
    /**
     * 收银台
     */
    @NotEmpty(message = "收银台不能为空")
    private String storeCashierPic;
    /**
     * 店内环境
     */
    @NotEmpty(message = "店内环境不能为空")
    private String storeEnvironmentPic;
}
