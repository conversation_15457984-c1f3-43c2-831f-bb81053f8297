/*
 * ailike.com
 * Copyright (C) 2022-2025 All Rights Reserved.
 */
package com.frt.usercore.domain.param;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serial;
import java.io.Serializable;

/**
 * 员工删除参数
 *
 * <AUTHOR>
 * @version UserDeleteParam.java, v 0.1 2025-08-27 16:51 zhangling
 */
@Data
public class UserDeleteParam implements Serializable {

    @Serial
    private static final long serialVersionUID = 4410310259438116286L;
    /**
     * 员工ID
     */
    @NotBlank(message = "员工ID不能为空")
    private String userId;

    /**
     * 商户ID
     */
    @NotBlank(message = "商户ID不能为空")
    private String merchantId;
}