/*
 * ailike.com
 * Copyright (C) 2022-2025 All Rights Reserved.
 */
package com.frt.usercore.domain.param;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version UserDisableAndEnableParam.java, v 0.1 2025-08-28 13:43 zhangling
 */
@Data
public class UserDisableAndEnableParam implements Serializable {
    private static final long serialVersionUID = 48843670069473905L;
    /**
     * 员工ID
     */
    @NotBlank(message = "员工ID不能为空")
    private String userId;

    /**
     * 商户ID
     */
    @NotBlank(message = "商户ID不能为空")
    private String merchantId;
}