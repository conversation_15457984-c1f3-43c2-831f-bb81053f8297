package com.frt.usercore.domain.result.merchantmina.auth;

import lombok.Getter;
import lombok.Setter;
import java.util.List;

/**
 * 登录页资源结果
 */
@Getter
@Setter
public class MerchantMinaResourceResult {
    private String tenantId;
    private String tenantName;
    private String brandLogo;
    private String backgroundImage;
    private String themeColor;
    private Boolean sendCode;
    private List<ProtocolInfo> protocolList;

    @Getter
    @Setter
    public static class ProtocolInfo {
        private String protocolId;
        private String protocolName;
        private String protocolType;
    }
}