package com.frt.usercore.domain.param.storemanager;

import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class StoreListQueryParam {

    /**
     * 商户ID
     */
    @NotBlank(message = "商户ID不能为空")
    private String merchantId;
    /**
     * 门店状态，门店是否展示 SHOW-展示 HIDE-隐藏
     */
    private String isShow;
    /**
     * 门店ID
     */
    private String storeId;
    /**
     * 门店名称
     */
    private String storeName;
}
