package com.frt.usercore.domain.param.rolemanager;

import lombok.Data;

import java.io.Serializable;

/**
 * 角色列表查询参数
 *
 * <AUTHOR>
 * @version RoleListQueryParam.java, v 0.1 2025-08-27 14:52 zhangling
 */
@Data
public class RoleListQueryParam implements Serializable {

    private static final long serialVersionUID = 3065410004318016366L;

    /**
     * 商户ID
     */
    private String merchantId;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 角色模板id（角色类型）
     */
    private String roleTemplateId;
}