/*
 * ailike.com
 * Copyright (C) 2022-2025 All Rights Reserved.
 */
package com.frt.usercore.domain.result.rolemanager;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version RoleInfoResult.java, v 0.1 2025-08-27 16:03 zhangling
 */
@Data
public class RoleInfoResult implements Serializable {
    private static final long serialVersionUID = 218941514585699832L;
    /**
     * 角色ID
     */
    private String roleId;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 角色描述
     */
    private String roleDesc;

    /**
     * 角色类型 0-超级管理员 1-普通角色
     */
    private Integer roleType;

    /**
     * 终端类型 1-商户端 2-运营端
     */
    private Integer terminalType;

    /**
     * 权限列表
     */
    private String permissions;

    /**
     * 状态 0-禁用 1-启用
     */
    private Integer status;

    /**
     * 备注
     */
    private String remark;
}