package com.frt.usercore.domain.mapper;

import com.frt.usercore.dao.entity.MerchantStoreInfoDO;
import com.frt.usercore.domain.param.storemanager.StoreInfoAddParam;
import com.frt.usercore.domain.param.storemanager.StoreInfoUpdateParam;
import com.frt.usercore.domain.result.UserStoreResult;
import com.frt.usercore.domain.result.storemanager.StoreInfoQueryResult;
import com.frt.usercore.domain.result.storemanager.StoreListQueryResult;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface StoreManagerMapper {

    StoreListQueryResult coverMerchantStoreInfoDOToStoreListQueryResult(MerchantStoreInfoDO infoDO);

    MerchantStoreInfoDO coverStoreInfoAddParamToStoreInfoDO(StoreInfoAddParam param);

    MerchantStoreInfoDO coverStoreInfoUpdateParamToStoreInfoDO(StoreInfoUpdateParam param);

    StoreInfoQueryResult coverStoreInfoUpdateParamToStoreInfoQueryResult(MerchantStoreInfoDO infoDO);

    UserStoreResult coverMerchantStoreInfoDOToUserStoreResult(MerchantStoreInfoDO infoDO);
}
