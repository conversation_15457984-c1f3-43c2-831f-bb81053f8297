/*
 * ailike.com
 * Copyright (C) 2022-2025 All Rights Reserved.
 */
package com.frt.usercore.domain.result.rolemanager;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version RolePermissionTreeResult.java, v 0.1 2025-08-29 16:30 zhangling
 */
@Data
public class RolePermissionTreeResult implements Serializable {
    private static final long serialVersionUID = -346785135526359067L;
    /**
     * 菜单ID
     */
    private String menuId;
    /**
     * 菜单名称
     */
    private String menuName;
    /**
     * 子菜单列表
     */
    private List<MerchantRolePermissionResult> childrenMenuList;
}