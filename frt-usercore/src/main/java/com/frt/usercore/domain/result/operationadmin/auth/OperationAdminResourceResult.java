package com.frt.usercore.domain.result.operationadmin.auth;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 登录页资源结果
 * 接口名称：operation/web/search/resource
 * 请求方式：POST
 */
@Getter
@Setter
public class OperationAdminResourceResult {
    
    /**
     * 租户名称
     */
    private String tenantName;
    
    /**
     * 品牌logo
     */
    private String brandLogo;
    
    /**
     * 背景图
     */
    private String backgroundImage;
    
    /**
     * 主题色
     */
    private String themeColor;
    
    /**
     * 是否有发送验证码能力
     */
    private Boolean sendCode;

    /**
     * 登录前交互token
     */
    private String token;
    
    /**
     * 协议列表
     */
    private List<ProtocolInfo> protocolList;

    @Getter
    @Setter
    public static class ProtocolInfo {
        /**
         * 协议id
         */
        private String protocolId;
        
        /**
         * 协议名称
         */
        private String protocolName;
        
        /**
         * 协议类型
         */
        private String protocolType;
    }
}