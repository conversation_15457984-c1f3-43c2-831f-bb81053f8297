package com.frt.usercore.api.operationadmin;

import com.frt.usercore.domain.param.operationadmin.forgotpassword.CheckSmsCodeParam;
import com.frt.usercore.domain.param.operationadmin.forgotpassword.CheckVerifyCodeParam;
import com.frt.usercore.domain.param.operationadmin.forgotpassword.GetVerifyCodeParam;
import com.frt.usercore.domain.param.operationadmin.forgotpassword.SendSmsParam;
import com.frt.usercore.domain.param.operationadmin.forgotpassword.UpdatePasswordParam;
import com.frt.usercore.domain.result.operationadmin.forgotpassword.CheckVerifyCodeResult;
import com.frt.usercore.domain.result.operationadmin.forgotpassword.GetVerifyCodeResult;
import com.frt.usercore.service.OperationAdminForgotPasswordService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 运营后台忘记密码API
 *
 * <AUTHOR>
 * @version OperationAdminForgotPasswordApi.java, v 0.1 2025-08-28 15:00 tuyuwei
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/operation/admin/forgot-password")
public class OperationAdminForgotPasswordApi {

    private final OperationAdminForgotPasswordService operationAdminForgotPasswordService;

    /**
     * 获取图形验证码
     *
     * @param param 请求参数
     * @return 验证码图片URL
     */
    @PostMapping("/get-verify-code")
    public GetVerifyCodeResult getVerifyCode(@RequestBody GetVerifyCodeParam param) {
        return operationAdminForgotPasswordService.getVerifyCode(param);
    }

    /**
     * 校验图文验证码
     *
     * @param param 请求参数
     * @return 校验结果
     */
    @PostMapping("/check-verify-code")
    public CheckVerifyCodeResult checkVerifyCode(@RequestBody CheckVerifyCodeParam param) {
        return operationAdminForgotPasswordService.checkVerifyCode(param);
    }

    /**
     * 发送短信
     *
     * @param param 请求参数
     */
    @PostMapping("/send-sms")
    public void sendSms(@RequestBody SendSmsParam param) {
        operationAdminForgotPasswordService.sendSms(param);
    }

    /**
     * 校验验证码
     *
     * @param param 请求参数
     */
    @PostMapping("/check-sms-code")
    public void checkSmsCode(@RequestBody CheckSmsCodeParam param) {
        operationAdminForgotPasswordService.checkSmsCode(param);
    }

    /**
     * 修改密码
     *
     * @param param 请求参数
     */
    @PostMapping("/update-password")
    public void updatePassword(@RequestBody UpdatePasswordParam param) {
        operationAdminForgotPasswordService.updatePassword(param);
    }
}
