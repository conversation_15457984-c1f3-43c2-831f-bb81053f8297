/*
 * ailike.com
 * Copyright (C) 2022-2025 All Rights Reserved.
 */
package com.frt.usercore.api.merchantadmin;

import com.frt.usercore.domain.param.PageParam;
import com.frt.usercore.domain.param.rolemanager.*;
import com.frt.usercore.domain.result.PageResult;
import com.frt.usercore.domain.result.common.CommonResult;
import com.frt.usercore.domain.result.rolemanager.MerchantCashierAndShopMenuPermissionResult;
import com.frt.usercore.domain.result.rolemanager.RoleDetailQueryResult;
import com.frt.usercore.domain.result.rolemanager.RoleInfoResult;
import com.frt.usercore.service.MerchantRolePermissionService;
import com.frt.usercore.service.RoleManagerService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version RoleManagerApi.java, v 0.1 2025-08-27 15:57 zhangling
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/role")
public class RoleManagerApi {

    private final RoleManagerService roleManagerService;

    private final MerchantRolePermissionService merchantRolePermissionService;

    /**
     * 查询角色列表
     *
     * @param param 请求参数
     * @return 角色列表
     */
    @PostMapping("/query-role-list")
    PageResult<RoleInfoResult> getRoleList(@RequestBody PageParam<RoleListQueryParam> param) {
        return roleManagerService.getRoleList(param);
    }

    /**
     * 查询角色详情
     *
     * @param param 请求参数
     * @return 角色详情
     */
    @PostMapping("/get-role-detail")
    RoleDetailQueryResult getRoleDetail(@RequestBody RoleDetailQueryParam param) {
        return roleManagerService.getRoleDetail(param);
    }

    /**
     * 添加角色
     *
     * @param param 请求参数
     * @return 操作结果
     */
    @PostMapping("/add-role")
    CommonResult addRole(@RequestBody RoleAddParam param) {
        return roleManagerService.addRole(param);
    }

    /**
     * 更新角色
     *
     * @param param 请求参数
     * @return 操作结果
     */
    @PostMapping("/update-role")
    CommonResult updateRole(@RequestBody RoleUpdateParam param) {
        return roleManagerService.updateRole(param);
    }

    /**
     * 删除角色
     *
     * @param param 请求参数
     * @return 操作结果
     */
    @PostMapping("/delete-role")
    CommonResult deleteRole(@RequestBody RoleDeleteParam param) {
        return roleManagerService.deleteRole(param);
    }

    /**
     * 获取权限菜单模板
     * @param param
     * @return
     */
    @PostMapping("/menu-permission-template")
    MerchantCashierAndShopMenuPermissionResult getPermissionTemplate(@RequestBody MerchantMenuPermissionParam param) {
        return merchantRolePermissionService.getPermissionTemplate(param);
    }
}