/**
 * <AUTHOR>
 * @date 2025/8/27 14:43
 * @version 1.0 MerchantMinaController
 */
package com.frt.usercore.api.merchantmina;

/**
 *
 *
 * <AUTHOR>
 * @version MerchantMinaController.java, v 0.1 2025-08-27 14:43 tuyuwei
 */

import com.frt.usercore.domain.param.merchantmina.auth.MerchantMinaChangePasswordParam;
import com.frt.usercore.domain.param.merchantmina.auth.MerchantMinaCheckCodeParam;
import com.frt.usercore.domain.param.merchantmina.auth.MerchantMinaLoginParam;
import com.frt.usercore.domain.param.merchantmina.auth.MerchantMinaResourceParam;
import com.frt.usercore.domain.param.merchantmina.auth.MerchantMinaSearchPhoneParam;
import com.frt.usercore.domain.param.merchantmina.auth.MerchantMinaSendCodeParam;
import com.frt.usercore.domain.result.merchantmina.auth.MerchantMinaLoginResult;
import com.frt.usercore.domain.result.merchantmina.auth.MerchantMinaResourceResult;
import com.frt.usercore.domain.result.merchantmina.auth.MerchantMinaSearchPhoneResult;
import com.frt.usercore.service.MerchantMinaAuthService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 商户小程序权限接口
 */
@RestController
@RequestMapping("/merchant/mina/auth")
public class MerchantMinaAuthApi {

    @Autowired
    private MerchantMinaAuthService merchantMinaAuthService;

    /**
     * 登录页资源获取接口
     *
     * @param param 请求参数
     * @return 资源信息
     */
    @PostMapping("/search/resource")
    public MerchantMinaResourceResult searchResource(@RequestBody MerchantMinaResourceParam param) {
        return merchantMinaAuthService.searchResource(param);
    }

    /**
     * 发送验证码
     *
     * @param param 请求参数
     * @return 发送结果
     */
    @PostMapping("/send/code")
    public void sendCode(@RequestBody MerchantMinaSendCodeParam param) {
        merchantMinaAuthService.sendCode(param);
    }

    /**
     * 账号登录
     *
     * @param param 登录参数
     * @return 登录结果
     */
    @PostMapping("/login")
    public MerchantMinaLoginResult login(@RequestBody MerchantMinaLoginParam param) {
        return merchantMinaAuthService.login(param);
    }

    /**
     * 通过账号查询加密手机号
     *
     * @param param 查询参数
     * @return 手机号信息
     */
    @PostMapping("/search/phone")
    public MerchantMinaSearchPhoneResult searchPhone(@RequestBody MerchantMinaSearchPhoneParam param) {
        return merchantMinaAuthService.searchPhone(param);
    }

    /**
     * 修改密码验证码校验
     *
     * @param param 验证参数
     * @return 校验结果
     */
    @PostMapping("/check/code")
    public void checkCode(@RequestBody MerchantMinaCheckCodeParam param) {
        merchantMinaAuthService.checkCode(param);
    }

    /**
     * 设置新密码
     *
     * @param param 修改密码参数
     * @return 修改结果
     */
    @PostMapping("/change/password")
    public void changePassword(@RequestBody MerchantMinaChangePasswordParam param) {
        merchantMinaAuthService.changePassword(param);
    }

    /**
     * 账号登出
     *
     * @return 登出结果
     */
    @PostMapping("/logout")
    public void logout() {
        merchantMinaAuthService.logout();
    }
}