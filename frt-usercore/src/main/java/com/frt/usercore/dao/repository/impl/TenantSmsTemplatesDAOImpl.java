package com.frt.usercore.dao.repository.impl;

import com.frt.usercore.dao.entity.TenantSmsTemplatesDO;
import com.frt.usercore.dao.mapper.TenantSmsTemplatesMapper;
import com.frt.usercore.dao.repository.TenantSmsTemplatesDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 租户短信模板表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-28
 */
@Service
public class TenantSmsTemplatesDAOImpl extends ServiceImpl<TenantSmsTemplatesMapper, TenantSmsTemplatesDO> implements TenantSmsTemplatesDAO {

    /**
     * 根据租户ID、场景值和模板类型查询短信模板
     *
     * @param tenantId     租户ID
     * @param sceneValue   场景值
     * @param templateType 模板类型
     * @return 短信模板
     */
    @Override
    public TenantSmsTemplatesDO getByTenantIdAndSceneValueAndTemplateType(String tenantId, String sceneValue, Integer templateType) {
        return query().eq(TenantSmsTemplatesDO.TENANT_ID, tenantId)
                .eq(TenantSmsTemplatesDO.SCENE_VALUE, sceneValue)
                .eq(TenantSmsTemplatesDO.TEMPLATE_TYPE, templateType)
                .one();
    }

}
