package com.frt.usercore.dao.repository.impl;

import com.frt.usercore.common.enums.business.DelFlagEnum;
import com.frt.usercore.dao.entity.TenantRoleDO;
import com.frt.usercore.dao.entity.TenantRoleTemplateDO;
import com.frt.usercore.dao.mapper.TenantRoleTemplateMapper;
import com.frt.usercore.dao.repository.TenantRoleTemplateDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 基础权限配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-29
 */
@Service
public class TenantRoleTemplateDAOImpl extends ServiceImpl<TenantRoleTemplateMapper, TenantRoleTemplateDO> implements TenantRoleTemplateDAO {

    /**
     * @param tenantId
     * @param templateId
     * @param platformType
     * @return
     */
    @Override
    public TenantRoleTemplateDO getByTenantIdAndTemplateIdAndPlatformType(String tenantId, String templateId, Integer platformType) {
        return this.lambdaQuery()
                .eq(TenantRoleTemplateDO::getTenantId, tenantId)
                .eq(TenantRoleTemplateDO::getRoleTemplateId, templateId)
                .eq(TenantRoleTemplateDO::getPlatformType, platformType)
                .eq(TenantRoleTemplateDO::getIsDel, DelFlagEnum.NOT_DELETED.getCode())
                .eq(TenantRoleTemplateDO::getIsEnabled, 1)
                .one();
    }
}
