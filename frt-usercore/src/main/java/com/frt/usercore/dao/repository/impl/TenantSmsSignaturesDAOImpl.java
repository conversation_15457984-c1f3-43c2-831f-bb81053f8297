package com.frt.usercore.dao.repository.impl;

import com.frt.usercore.dao.entity.TenantSmsSignaturesDO;
import com.frt.usercore.dao.mapper.TenantSmsSignaturesMapper;
import com.frt.usercore.dao.repository.TenantSmsSignaturesDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 租户短信签名表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-28
 */
@Service
public class TenantSmsSignaturesDAOImpl extends ServiceImpl<TenantSmsSignaturesMapper, TenantSmsSignaturesDO> implements TenantSmsSignaturesDAO {

}
