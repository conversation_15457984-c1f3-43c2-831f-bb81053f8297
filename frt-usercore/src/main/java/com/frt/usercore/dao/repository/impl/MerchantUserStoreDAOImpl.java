package com.frt.usercore.dao.repository.impl;

import com.frt.usercore.dao.entity.MerchantUserDO;
import com.frt.usercore.dao.entity.MerchantUserStoreDO;
import com.frt.usercore.dao.mapper.MerchantUserStoreMapper;
import com.frt.usercore.dao.repository.MerchantUserStoreDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 商户员工门店关系表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-28
 */
@Service
public class MerchantUserStoreDAOImpl extends ServiceImpl<MerchantUserStoreMapper, MerchantUserStoreDO> implements MerchantUserStoreDAO {

    @Override
    public List<MerchantUserStoreDO> findByUserIdAndMerchantId(String userId, String merchantId) {
        this.lambdaQuery()
                .eq(MerchantUserStoreDO::getUserId, userId)
                .eq(MerchantUserStoreDO::getMerchantId, userId)
                .eq(MerchantUserStoreDO::getIsDel, 0)
                .one();
    }
}
