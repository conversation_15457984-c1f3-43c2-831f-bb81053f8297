package com.frt.usercore.dao.repository;

import com.frt.usercore.dao.entity.TenantRoleTemplateDO;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 基础权限配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-29
 */
public interface TenantRoleTemplateDAO extends IService<TenantRoleTemplateDO> {

    /**
     *
     * @param tenantId
     * @param templateId
     * @param platformType
     * @return
     */
    TenantRoleTemplateDO getByTenantIdAndTemplateIdAndPlatformType(String tenantId, String templateId, Integer platformType);
}
