package com.frt.usercore.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <p>
 * 门店信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-27
 */
@Data
@TableName("frt_merchant_store_info")
public class MerchantStoreInfoDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 门店id
     */
    @TableField("store_id")
    private String storeId;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 商户id
     */
    @TableField("merchant_id")
    private String merchantId;

    /**
     * 门店名称
     */
    @TableField("store_name")
    private String storeName;

    /**
     * 统一类目id，关联frt_unity_category
     */
    @TableField("unity_cat_id")
    private Integer unityCatId;

    /**
     * 门店联系电话
     */
    @TableField("store_phone")
    private String storePhone;

    /**
     * 省code
     */
    @TableField("province_code")
    private String provinceCode;

    /**
     * 市code
     */
    @TableField("city_code")
    private String cityCode;

    /**
     * 县code
     */
    @TableField("county_code")
    private String countyCode;

    /**
     * 省
     */
    @TableField("province_name")
    private String provinceName;

    /**
     * 市
     */
    @TableField("city_name")
    private String cityName;

    /**
     * 县
     */
    @TableField("county_name")
    private String countyName;

    /**
     * 门店地址
     */
    @TableField("store_address")
    private String storeAddress;

    /**
     * 门店门头照片
     */
    @TableField("store_door_pic")
    private String storeDoorPic;

    /**
     * 门店收银台照片
     */
    @TableField("store_cashier_pic")
    private String storeCashierPic;

    /**
     * 门店店内环境照
     */
    @TableField("store_environment_pic")
    private String storeEnvironmentPic;

    /**
     * 门店是否展示 SHOW-展示 HIDE-隐藏
     */
    @TableField("is_show")
    private String isShow;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 逻辑删除标志(0:未删除, 1:已删除)
     */
    @TableField("is_del")
    @TableLogic
    private Integer isDel;

    public Date getCreateTime() {
        if (this.createTime != null) {
          return new Date(this.createTime.getTime());
        } else {
          return null;
        }
    }

    public void setCreateTime(Date createTime) {
        if (createTime != null) {
            this.createTime = new Date(createTime.getTime());
        } else {
            this.createTime = null;
        }
    }
    public Date getUpdateTime() {
        if (this.updateTime != null) {
          return new Date(this.updateTime.getTime());
        } else {
          return null;
        }
    }

    public void setUpdateTime(Date updateTime) {
        if (updateTime != null) {
            this.updateTime = new Date(updateTime.getTime());
        } else {
            this.updateTime = null;
        }
    }


    public static final String ID = "id";

    public static final String STORE_ID = "store_id";

    public static final String TENANT_ID = "tenant_id";

    public static final String MERCHANT_ID = "merchant_id";

    public static final String STORE_NAME = "store_name";

    public static final String UNITY_CAT_ID = "unity_cat_id";

    public static final String STORE_PHONE = "store_phone";

    public static final String PROVINCE_CODE = "province_code";

    public static final String CITY_CODE = "city_code";

    public static final String COUNTY_CODE = "county_code";

    public static final String PROVINCE_NAME = "province_name";

    public static final String CITY_NAME = "city_name";

    public static final String COUNTY_NAME = "county_name";

    public static final String STORE_ADDRESS = "store_address";

    public static final String STORE_DOOR_PIC = "store_door_pic";

    public static final String STORE_CASHIER_PIC = "store_cashier_pic";

    public static final String STORE_ENVIRONMENT_PIC = "store_environment_pic";

    public static final String IS_SHOW = "is_show";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

    public static final String IS_DEL = "is_del";

}
