package com.frt.usercore.dao.repository.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.frt.usercore.common.enums.business.DelFlagEnum;
import com.frt.usercore.dao.entity.TenantRoleDO;
import com.frt.usercore.dao.mapper.TenantRoleMapper;
import com.frt.usercore.dao.repository.TenantRoleDAO;
import com.frt.usercore.domain.dto.param.RoleListQueryParamDTO;
import com.frt.usercore.domain.dto.result.FindRoleByUserIdListResultDTO;
import com.frt.usercore.domain.param.PageParam;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 租户角色表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-28
 */
@Service
public class TenantRoleDAOImpl extends ServiceImpl<TenantRoleMapper, TenantRoleDO> implements TenantRoleDAO {

    @Override
    public TenantRoleDO getByRoleId(String roleId) {
        return this.lambdaQuery()
                .eq(TenantRoleDO::getRoleId, roleId)
                .eq(TenantRoleDO::getStatus, 1)
                .one();
    }

	/**
	 * 分页查询角色列表
	 *
	 * @param pageDTO 分页参数
	 * @return 分页结果
	 */
	@Override
	public Page<TenantRoleDO> findPageList(PageParam<RoleListQueryParamDTO> pageDTO) {
		Page<RoleListQueryParamDTO> page = new Page<>();
		page.setCurrent(pageDTO.getPage());
		page.setSize(pageDTO.getPageSize());
		return getBaseMapper().findPageList(page, pageDTO.getQuery());
	}

	/**
	 * 根据角色名称查询角色
	 *
	 * @param roleName 角色名称
	 * @param tenantId 租户ID
	 * @param roleType 角色类型 1-运营 2-代理商 3-商户
	 * @return 角色
	 */
	@Override
	public TenantRoleDO getByRoleName(String roleName, String tenantId, Integer roleType, String roleId) {
		return query().eq(TenantRoleDO.ROLE_NAME, roleName)
				.eq(TenantRoleDO.TENANT_ID, tenantId)
				.eq(TenantRoleDO.ROLE_TYPE, roleType)
				.eq(TenantRoleDO.IS_DEL, DelFlagEnum.NOT_DELETED.getCode())
				.ne(StringUtils.isNotBlank(roleId), TenantRoleDO.ROLE_ID, roleId)
				.one();
	}

	/**
	 * 根据角色ID删除角色
	 *
	 * @param roleId
	 * @return
	 */
	@Override
	public boolean removeByRoleId(String roleId) {
		return update()
				.set(TenantRoleDO.IS_DEL, DelFlagEnum.DELETED.getCode())
				.eq(TenantRoleDO.ROLE_ID, roleId)
				.update();
	}

	/**
	 * 根据 userId 查询角色名称
	 *
	 * @param userIdList 用户id列表
	 * @return 角色名称
	 */
	@Override
	public List<FindRoleByUserIdListResultDTO> findRoleByUserIdList(List<String> userIdList) {
		return getBaseMapper().findRoleByUserIdList(userIdList);
	}

	/**
	 * 根据租户ID和角色ID查询角色
	 *
	 * @param tenantId
	 * @param roleId
	 * @return
	 */
	@Override
	public TenantRoleDO getByTenantIdAndRoleIdAndRoleType(String tenantId, String roleId, Integer roleType) {
		return query().eq(TenantRoleDO.TENANT_ID, tenantId)
				.eq(TenantRoleDO.ROLE_TYPE, roleType)
				.eq(TenantRoleDO.ROLE_ID, roleId)
				.eq(TenantRoleDO.IS_DEL, DelFlagEnum.NOT_DELETED.getCode())
				.one();
	}

	@Override
	public List<String> findRoleIdByUserId(String tenantId,String userId) {
		return getBaseMapper().findRoleIdByUserId(tenantId, userId);
	}


}
