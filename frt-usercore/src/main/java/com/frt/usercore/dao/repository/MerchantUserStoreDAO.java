package com.frt.usercore.dao.repository;

import com.frt.usercore.dao.entity.MerchantUserStoreDO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 商户员工门店关系表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-28
 */
public interface MerchantUserStoreDAO extends IService<MerchantUserStoreDO> {

    List<MerchantUserStoreDO> findByUserIdAndMerchantId(String userId, String merchantId);

}
