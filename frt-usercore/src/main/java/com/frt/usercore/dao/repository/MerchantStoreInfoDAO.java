package com.frt.usercore.dao.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.frt.usercore.dao.entity.MerchantStoreInfoDO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.frt.usercore.domain.param.PageParam;
import com.frt.usercore.domain.param.storemanager.StoreListQueryParam;

import java.util.List;

/**
 * <p>
 * 门店信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-27
 */
public interface MerchantStoreInfoDAO extends IService<MerchantStoreInfoDO> {

    /**
     * 根据门店id和商户id查询门店信息
     * @param storeIdList
     * @param merchantId
     * @return
     */
    List<MerchantStoreInfoDO> findByStoreIdListAndMerchantId(List<String> storeIdList, String merchantId);

    /**
     * 查询门店分页列表
     * @param param
     * @return
     */
    Page<MerchantStoreInfoDO> findStorePageWithTenantId(PageParam<StoreListQueryParam> param);

    /**
     * 根据门店ID查询详情
     * @param storeId
     * @return
     */
    MerchantStoreInfoDO getInfoByStoreIdWithTenantId(String storeId);

    /**
     * 根据门店名称查询详情
     * @param storeName
     * @return
     */
    MerchantStoreInfoDO getInfoByStoreName(String storeName);

    /**
     * 根据门店ID更新门店信息
     * @param storeInfoDO
     */
    void updateByStoreIdAndMerchantIdWithTenantId(MerchantStoreInfoDO storeInfoDO);
}
