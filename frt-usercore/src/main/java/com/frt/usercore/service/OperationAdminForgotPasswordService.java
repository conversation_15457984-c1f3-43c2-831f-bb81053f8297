package com.frt.usercore.service;

import com.frt.usercore.domain.param.operationadmin.forgotpassword.CheckSmsCodeParam;
import com.frt.usercore.domain.param.operationadmin.forgotpassword.CheckVerifyCodeParam;
import com.frt.usercore.domain.param.operationadmin.forgotpassword.GetVerifyCodeParam;
import com.frt.usercore.domain.param.operationadmin.forgotpassword.SendSmsParam;
import com.frt.usercore.domain.param.operationadmin.forgotpassword.UpdatePasswordParam;
import com.frt.usercore.domain.result.operationadmin.forgotpassword.CheckVerifyCodeResult;
import com.frt.usercore.domain.result.operationadmin.forgotpassword.GetVerifyCodeResult;

/**
 * 运营后台忘记密码服务接口
 *
 * <AUTHOR>
 * @version OperationAdminForgotPasswordService.java, v 0.1 2025-08-28 15:00 tuyuwei
 */
public interface OperationAdminForgotPasswordService {

    /**
     * 获取图形验证码
     *
     * @param param 请求参数
     * @return 验证码图片URL
     */
    GetVerifyCodeResult getVerifyCode(GetVerifyCodeParam param);

    /**
     * 校验图文验证码
     *
     * @param param 请求参数
     * @return 校验结果
     */
    CheckVerifyCodeResult checkVerifyCode(CheckVerifyCodeParam param);

    /**
     * 发送短信
     *
     * @param param 请求参数
     */
    void sendSms(SendSmsParam param);

    /**
     * 校验验证码
     *
     * @param param 请求参数
     */
    void checkSmsCode(CheckSmsCodeParam param);

    /**
     * 修改密码
     *
     * @param param 请求参数
     */
    void updatePassword(UpdatePasswordParam param);
}
