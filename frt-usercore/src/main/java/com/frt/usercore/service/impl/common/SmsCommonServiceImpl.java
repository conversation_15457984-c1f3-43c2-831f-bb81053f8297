package com.frt.usercore.service.impl.common;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.frt.usercore.common.constants.RedisPrefixConstant;
import com.frt.usercore.common.enums.business.MessageTemplateEnum;
import com.frt.usercore.common.enums.business.SmsChannelTypeEnum;
import com.frt.usercore.common.enums.business.SmsTemplateTypeEnum;
import com.frt.usercore.common.enums.exception.base.ErrorCodeEnum;
import com.frt.usercore.common.exception.InternalException;
import com.frt.usercore.common.utils.LogUtil;
import com.frt.usercore.dao.entity.TenantSmsChannelConfigsDO;
import com.frt.usercore.dao.entity.TenantSmsSignaturesDO;
import com.frt.usercore.dao.entity.TenantSmsTemplatesDO;
import com.frt.usercore.dao.repository.TenantSmsChannelConfigsDAO;
import com.frt.usercore.dao.repository.TenantSmsSignaturesDAO;
import com.frt.usercore.dao.repository.TenantSmsTemplatesDAO;
import com.frt.usercore.domain.param.common.SmsMessageCodeParam;
import com.frt.usercore.domain.param.common.SmsMessageParam;
import com.frt.usercore.domain.param.common.SmsMessageTemplateParam;
import com.frt.usercore.manager.SmsManager;
import com.frt.usercore.service.common.SmsCommonService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
@AllArgsConstructor
public class SmsCommonServiceImpl implements SmsCommonService {

	private final TenantSmsTemplatesDAO tenantSmsTemplatesDAO;
	private final TenantSmsSignaturesDAO tenantSmsSignaturesDAO;
	private final TenantSmsChannelConfigsDAO tenantSmsChannelConfigsDAO;
	private final StringRedisTemplate stringRedisTemplate;

	/**
	 * 验证码长度
	 */
	private static final int VERIFICATION_CODE_LENGTH = 6;

	/**
	 * 验证码有效期（分钟）
	 */
	private static final int VERIFICATION_CODE_EXPIRE_MINUTES = 5;

	/**
	 * 发送短信
	 *
	 * @param phone      手机号
	 * @param sceneValue 场景值
	 * @param tenantId   租户ID
	 * @return 发送结果
	 * @throws InternalException 参数校验失败或业务异常
	 */
	@Override
	public Boolean sendSms(String phone, String sceneValue, String tenantId, SmsMessageTemplateParam param) {
		LogUtil.info(log, "SmsCommonServiceImpl.sendSms >> 接口开始 >> phone = {}, sceneValue = {}, tenantId = {}, param = {}",
				phone, sceneValue, tenantId,param);

		try {
			// 参数校验
			validateSendSmsParams(phone, sceneValue, tenantId);

			// 查询短信模板配置
			TenantSmsTemplatesDO smsTemplate = getSmsTemplate(tenantId, sceneValue);
			if (smsTemplate == null) {
				LogUtil.error(log, "SmsCommonServiceImpl.sendSms >> 未找到短信模板配置 >> tenantId = {}, sceneValue = {}",
						tenantId, sceneValue);
				throw new InternalException(ErrorCodeEnum.PARAMETER_VALIDATION_FAILED.getErrorCode(), "PARAM_ERROR", "未找到短信模板配置");
			}

			// 查询签名信息
			TenantSmsSignaturesDO smsSignature = getSmsSignature(tenantId, smsTemplate.getSignatureCode());
			if (smsSignature == null) {
				LogUtil.error(log, "SmsCommonServiceImpl.sendSms >> 未找到短信签名配置 >> tenantId = {}, signatureCode = {}",
						tenantId, smsTemplate.getSignatureCode());
				throw new InternalException(ErrorCodeEnum.PARAMETER_VALIDATION_FAILED.getErrorCode(), "PARAM_ERROR", "未找到短信签名配置");
			}

			// 查询通道配置
			TenantSmsChannelConfigsDO channelConfig = getSmsChannelConfig(tenantId, smsTemplate.getChannelType());
			if (channelConfig == null) {
				LogUtil.error(log, "SmsCommonServiceImpl.sendSms >> 未找到短信通道配置 >> tenantId = {}, channelType = {}",
						tenantId, smsTemplate.getChannelType());
				throw new InternalException(ErrorCodeEnum.PARAMETER_VALIDATION_FAILED.getErrorCode(), "PARAM_ERROR", "未找到短信通道配置");
			}
			sendSmsToThirdParty(phone, smsTemplate, smsSignature, channelConfig,param);
			LogUtil.info(log, "SmsCommonServiceImpl.sendSms >> 短信发送成功 >> phone = {}", phone);
			return true;

		} catch (InternalException e) {
			LogUtil.error(log, "SmsCommonServiceImpl.sendSms >> 业务异常 >> error = {}", e.getMessage());
			throw e;
		} catch (Exception e) {
			LogUtil.error(log, "SmsCommonServiceImpl.sendSms >> 系统异常 >> error = {}", e.getMessage(), e);
			throw new InternalException(ErrorCodeEnum.SYS_ERROR.getErrorCode(), "SYS_ERROR", "短信发送异常");
		}
	}

	/**
	 * 校验短信验证码
	 *
	 * @param phone      手机号
	 * @param code       验证码
	 * @param sceneValue 场景值
	 * @param tenantId   租户ID
	 * @return 校验结果
	 * @throws InternalException 参数校验失败或业务异常
	 */
	@Override
	public Boolean checkSmsCode(String phone, String code, String sceneValue, String tenantId) {
		LogUtil.info(log, "SmsCommonServiceImpl.checkSmsCode >> 接口开始 >> phone = {}, code = {}, sceneValue = {}, tenantId = {}",
				phone, code, sceneValue, tenantId);

		try {
			// 参数校验
			validateCheckSmsCodeParams(phone, code, sceneValue, tenantId);

			// 从Redis获取存储的验证码
			String storedCode = getVerificationCodeFromRedis(phone, sceneValue, tenantId);
			if (StrUtil.isBlank(storedCode)) {
				LogUtil.error(log, "SmsCommonServiceImpl.checkSmsCode >> 验证码不存在或已过期 >> phone = {}, sceneValue = {}",
						phone, sceneValue);
				return false;
			}

			// 校验验证码
			boolean isValid = code.equals(storedCode);
			if (isValid) {
				// 验证成功，删除Redis中的验证码
				deleteVerificationCodeFromRedis(phone, sceneValue, tenantId);
				LogUtil.info(log, "SmsCommonServiceImpl.checkSmsCode >> 验证码校验成功 >> phone = {}", phone);
			} else {
				LogUtil.error(log, "SmsCommonServiceImpl.checkSmsCode >> 验证码校验失败 >> phone = {}, inputCode = {}, storedCode = {}",
						phone, code, storedCode);
			}

			return isValid;

		} catch (InternalException e) {
			LogUtil.error(log, "SmsCommonServiceImpl.checkSmsCode >> 业务异常 >> error = {}", e.getMessage());
			throw e;
		} catch (Exception e) {
			LogUtil.error(log, "SmsCommonServiceImpl.checkSmsCode >> 系统异常 >> error = {}", e.getMessage(), e);
			throw new InternalException(ErrorCodeEnum.SYS_ERROR.getErrorCode(), "SYS_ERROR", "验证码校验异常");
		}
	}

	/**
	 * 校验发送短信参数
	 *
	 * @param phone      手机号
	 * @param sceneValue 场景值
	 * @param tenantId   租户ID
	 * @throws InternalException 参数校验失败
	 */
	private void validateSendSmsParams(String phone, String sceneValue, String tenantId) {
		if (StrUtil.isBlank(phone)) {
			throw new InternalException(ErrorCodeEnum.PARAMETER_VALIDATION_FAILED.getErrorCode(), "PARAM_ERROR", "手机号不能为空");
		}
		if (StrUtil.isBlank(sceneValue)) {
			throw new InternalException(ErrorCodeEnum.PARAMETER_VALIDATION_FAILED.getErrorCode(), "PARAM_ERROR", "场景值不能为空");
		}
		if (StrUtil.isBlank(tenantId)) {
			throw new InternalException(ErrorCodeEnum.PARAMETER_VALIDATION_FAILED.getErrorCode(), "PARAM_ERROR", "租户ID不能为空");
		}
	}

	/**
	 * 校验验证码校验参数
	 *
	 * @param phone      手机号
	 * @param code       验证码
	 * @param sceneValue 场景值
	 * @param tenantId   租户ID
	 * @throws InternalException 参数校验失败
	 */
	private void validateCheckSmsCodeParams(String phone, String code, String sceneValue, String tenantId) {
		if (StrUtil.isBlank(phone)) {
			throw new InternalException(ErrorCodeEnum.PARAMETER_VALIDATION_FAILED.getErrorCode(), "PARAM_ERROR", "手机号不能为空");
		}
		if (StrUtil.isBlank(code)) {
			throw new InternalException(ErrorCodeEnum.PARAMETER_VALIDATION_FAILED.getErrorCode(), "PARAM_ERROR", "验证码不能为空");
		}
		if (StrUtil.isBlank(sceneValue)) {
			throw new InternalException(ErrorCodeEnum.PARAMETER_VALIDATION_FAILED.getErrorCode(), "PARAM_ERROR", "场景值不能为空");
		}
		if (StrUtil.isBlank(tenantId)) {
			throw new InternalException(ErrorCodeEnum.PARAMETER_VALIDATION_FAILED.getErrorCode(), "PARAM_ERROR", "租户ID不能为空");
		}
	}

	/**
	 * 获取短信模板配置
	 *
	 * @param tenantId   租户ID
	 * @param sceneValue 场景值
	 * @return 短信模板配置
	 */
	private TenantSmsTemplatesDO getSmsTemplate(String tenantId, String sceneValue) {
		LogUtil.info(log, "SmsCommonServiceImpl.getSmsTemplate >> 查询短信模板 >> tenantId = {}, sceneValue = {}",
				tenantId, sceneValue);

		LambdaQueryWrapper<TenantSmsTemplatesDO> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(TenantSmsTemplatesDO::getTenantId, tenantId)
				.eq(TenantSmsTemplatesDO::getSceneValue, sceneValue)
				.eq(TenantSmsTemplatesDO::getTemplateType, MessageTemplateEnum.VERIFICATION_CODE.getCode());

		List<TenantSmsTemplatesDO> templates = tenantSmsTemplatesDAO.list(queryWrapper);
		if (CollectionUtil.isNotEmpty(templates)) {
			return templates.get(0);
		}
		return null;
	}

	/**
	 * 获取短信签名配置
	 *
	 * @param tenantId      租户ID
	 * @param signatureCode 签名编码
	 * @return 短信签名配置
	 */
	private TenantSmsSignaturesDO getSmsSignature(String tenantId, String signatureCode) {
		LogUtil.info(log, "SmsCommonServiceImpl.getSmsSignature >> 查询短信签名 >> tenantId = {}, signatureCode = {}",
				tenantId, signatureCode);

		LambdaQueryWrapper<TenantSmsSignaturesDO> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(TenantSmsSignaturesDO::getTenantId, tenantId)
				.eq(TenantSmsSignaturesDO::getSignatureCode, signatureCode);

		List<TenantSmsSignaturesDO> signatures = tenantSmsSignaturesDAO.list(queryWrapper);
		if (CollectionUtil.isNotEmpty(signatures)) {
			return signatures.get(0);
		}
		return null;
	}

	/**
	 * 获取短信通道配置
	 *
	 * @param tenantId    租户ID
	 * @param channelType 通道类型
	 * @return 短信通道配置
	 */
	private TenantSmsChannelConfigsDO getSmsChannelConfig(String tenantId, String channelType) {
		LogUtil.info(log, "SmsCommonServiceImpl.getSmsChannelConfig >> 查询短信通道配置 >> tenantId = {}, channelType = {}",
				tenantId, channelType);

		LambdaQueryWrapper<TenantSmsChannelConfigsDO> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(TenantSmsChannelConfigsDO::getTenantId, tenantId)
				.eq(TenantSmsChannelConfigsDO::getChannelType, channelType);

		List<TenantSmsChannelConfigsDO> configs = tenantSmsChannelConfigsDAO.list(queryWrapper);
		if (CollectionUtil.isNotEmpty(configs)) {
			return configs.get(0);
		}
		return null;
	}

	/**
	 * 生成验证码
	 *
	 * @return 6位数字验证码
	 */
	private String generateVerificationCode() {
		return RandomUtil.randomNumbers(VERIFICATION_CODE_LENGTH);
	}

	/**
	 * 替换短信模板中的验证码参数
	 *
	 * @param templateContent    模板内容
	 * @param verificationCode   验证码
	 * @return 替换后的短信内容
	 */
	private String replaceSmsTemplate(String templateContent, String verificationCode) {
		if (StrUtil.isBlank(templateContent)) {
			return templateContent;
		}
		// 替换模板中的${code}参数
		return templateContent.replace("${code}", verificationCode);
	}

	/**
	 * 发送短信到第三方服务
	 *
	 * @param phone         手机号
	 * @param smsSignature  短信签名
	 * @param channelConfig 通道配置
	 * @return 发送结果
	 */
	private boolean sendSmsToThirdParty(String phone, TenantSmsTemplatesDO smsTemplate, TenantSmsSignaturesDO smsSignature,
			TenantSmsChannelConfigsDO channelConfig,SmsMessageTemplateParam param) {

		try {
			// 例如：阿里云短信、腾讯云短信等
			String smsChannelType = channelConfig.getChannelType();
			SmsChannelTypeEnum smsChannelTypeEnum = SmsChannelTypeEnum.getByChannelType(smsChannelType);
			if (smsChannelTypeEnum == null) {
				LogUtil.error(log, "SmsCommonServiceImpl.sendSmsToThirdParty >> 无效的短信通道类型 >> channelType = {}", smsChannelType);
				return false;
			}
			// TODO YXR 2025/8/29 短信模板类型要构建验证码  
			SmsManager smsManager = SpringUtil.getBean(smsChannelTypeEnum.getSmsManager());
			SmsMessageParam smsMessageParam = new SmsMessageParam();
			smsMessageParam.setParam(param);
			if (SmsTemplateTypeEnum.VERIFICATION_CODE.getType().equals(smsTemplate.getTemplateType())){
				// 	构建验证码
				String verificationCode = generateVerificationCode();
				smsMessageParam.setParam(new SmsMessageCodeParam(verificationCode));
				// 	存储验证码
				storeVerificationCodeToRedis(phone, smsTemplate.getSceneValue(), smsTemplate.getTenantId(), verificationCode);
			}
			smsMessageParam.setPhoneNumber(phone);
			smsMessageParam.setChannelConfig(channelConfig);
			smsMessageParam.setSmsTemplate(smsTemplate);
			smsMessageParam.setSmsSignature(smsSignature);
			smsManager.sendMessage(smsMessageParam);

			LogUtil.info(log, "SmsCommonServiceImpl.sendSmsToThirdParty >> 第三方短信发送成功 >> phone = {}", phone);
			return true;

		} catch (Exception e) {
			LogUtil.error(log, "SmsCommonServiceImpl.sendSmsToThirdParty >> 第三方短信发送异常 >> phone = {}, error = {}",
					phone, e.getMessage(), e);
			return false;
		}
	}

	/**
	 * 将验证码存储到Redis
	 *
	 * @param phone              手机号
	 * @param sceneValue         场景值
	 * @param tenantId           租户ID
	 * @param verificationCode   验证码
	 */
	private void storeVerificationCodeToRedis(String phone, String sceneValue, String tenantId, String verificationCode) {
		try {
			String cacheKey = buildVerificationCodeCacheKey(phone, sceneValue, tenantId);
			stringRedisTemplate.opsForValue().set(cacheKey, verificationCode, VERIFICATION_CODE_EXPIRE_MINUTES, TimeUnit.MINUTES);

			LogUtil.info(log, "SmsCommonServiceImpl.storeVerificationCodeToRedis >> 验证码存储成功 >> cacheKey = {}, expireMinutes = {}",
					cacheKey, VERIFICATION_CODE_EXPIRE_MINUTES);
		} catch (Exception e) {
			LogUtil.error(log, "SmsCommonServiceImpl.storeVerificationCodeToRedis >> 验证码存储异常 >> phone = {}, error = {}",
					phone, e.getMessage(), e);
			throw new InternalException(ErrorCodeEnum.SYS_ERROR.getErrorCode(), "SYS_ERROR", "验证码存储失败");
		}
	}

	/**
	 * 从Redis获取验证码
	 *
	 * @param phone      手机号
	 * @param sceneValue 场景值
	 * @param tenantId   租户ID
	 * @return 验证码
	 */
	private String getVerificationCodeFromRedis(String phone, String sceneValue, String tenantId) {
		try {
			String cacheKey = buildVerificationCodeCacheKey(phone, sceneValue, tenantId);
			String verificationCode = stringRedisTemplate.opsForValue().get(cacheKey);

			LogUtil.info(log, "SmsCommonServiceImpl.getVerificationCodeFromRedis >> 获取验证码 >> cacheKey = {}, hasCode = {}",
					cacheKey, StrUtil.isNotBlank(verificationCode));
			return verificationCode;
		} catch (Exception e) {
			LogUtil.error(log, "SmsCommonServiceImpl.getVerificationCodeFromRedis >> 获取验证码异常 >> phone = {}, error = {}",
					phone, e.getMessage(), e);
			return null;
		}
	}

	/**
	 * 从Redis删除验证码
	 *
	 * @param phone      手机号
	 * @param sceneValue 场景值
	 * @param tenantId   租户ID
	 */
	private void deleteVerificationCodeFromRedis(String phone, String sceneValue, String tenantId) {
		try {
			String cacheKey = buildVerificationCodeCacheKey(phone, sceneValue, tenantId);
			stringRedisTemplate.delete(cacheKey);

			LogUtil.info(log, "SmsCommonServiceImpl.deleteVerificationCodeFromRedis >> 删除验证码成功 >> cacheKey = {}", cacheKey);
		} catch (Exception e) {
			LogUtil.error(log, "SmsCommonServiceImpl.deleteVerificationCodeFromRedis >> 删除验证码异常 >> phone = {}, error = {}",
					phone, e.getMessage(), e);
		}
	}

	/**
	 * 构建验证码缓存键
	 *
	 * @param phone      手机号
	 * @param sceneValue 场景值
	 * @param tenantId   租户ID
	 * @return 缓存键
	 */
	private String buildVerificationCodeCacheKey(String phone, String sceneValue, String tenantId) {
		return StrUtil.format(RedisPrefixConstant.SMS_VERIFICATION_CODE, tenantId, phone, sceneValue);
	}
}
