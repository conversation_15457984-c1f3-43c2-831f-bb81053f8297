package com.frt.usercore.service.common;

import com.frt.usercore.domain.result.common.CheckLoginPasswordResult;

public interface PasswordCommonService {


	/**
	 * 校验登录密码
	 * @param account 账号
	 * @param password 密码
	 * @param platformType 平台类型
	 * @param tenantId 租户ID
	 * @return 校验结果
	 */
	CheckLoginPasswordResult checkLoginPassword(String account, String password, String platformType, String tenantId);

	/**
	 * 重置登录错误次数
	 * @param account 账号
	 * @param platformType 平台类型
	 * @param tenantId 租户ID
	 */
	void resetLoginErrorCount(String account, String platformType, String tenantId);

}
