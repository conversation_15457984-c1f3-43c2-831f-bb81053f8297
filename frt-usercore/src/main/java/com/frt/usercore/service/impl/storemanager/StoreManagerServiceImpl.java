package com.frt.usercore.service.impl.storemanager;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.frt.usercore.common.utils.IdWorkerUtil;
import com.frt.usercore.common.utils.TenantContextUtil;
import com.frt.usercore.common.utils.ValidateUtil;
import com.frt.usercore.dao.entity.GaodeCodeDO;
import com.frt.usercore.dao.entity.MerchantDO;
import com.frt.usercore.dao.entity.MerchantStoreInfoDO;
import com.frt.usercore.dao.entity.UnityCategoryDO;
import com.frt.usercore.dao.repository.GaodeCodeDAO;
import com.frt.usercore.dao.repository.MerchantDAO;
import com.frt.usercore.dao.repository.MerchantStoreInfoDAO;
import com.frt.usercore.dao.repository.UnityCategoryDAO;
import com.frt.usercore.domain.mapper.StoreManagerMapper;
import com.frt.usercore.domain.param.PageParam;
import com.frt.usercore.domain.param.storemanager.StoreInfoAddParam;
import com.frt.usercore.domain.param.storemanager.StoreInfoQueryParam;
import com.frt.usercore.domain.param.storemanager.StoreInfoUpdateParam;
import com.frt.usercore.domain.param.storemanager.StoreListQueryParam;
import com.frt.usercore.domain.result.PageResult;
import com.frt.usercore.domain.result.storemanager.StoreInfoAddResult;
import com.frt.usercore.domain.result.storemanager.StoreInfoQueryResult;
import com.frt.usercore.domain.result.storemanager.StoreInfoUpdateResult;
import com.frt.usercore.domain.result.storemanager.StoreListQueryResult;
import com.frt.usercore.service.storemanager.StoreManagerService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class StoreManagerServiceImpl implements StoreManagerService {

    @Autowired
    private MerchantDAO merchantDAO;
    @Autowired
    private GaodeCodeDAO gaodeCodeDAO;
    @Autowired
    private UnityCategoryDAO unityCategoryDAO;
    @Autowired
    private MerchantStoreInfoDAO merchantStoreInfoDAO;

    @Autowired
    private StoreManagerMapper storeManagerMapper;


    @Override
    public PageResult<StoreListQueryResult> queryStoreList(PageParam<StoreListQueryParam> param) {

        StoreListQueryParam query = param.getQuery();
        ValidateUtil.validate(query);

        Page<MerchantStoreInfoDO> pageDO = merchantStoreInfoDAO.findStorePageWithTenantId(param);
        if (CollectionUtil.isEmpty(pageDO.getRecords())) {
            PageResult<StoreListQueryResult> pageResult = new PageResult<>();
            pageResult.setTotal(pageDO.getTotal());
            pageResult.setSize(pageDO.getSize());
            pageResult.setCurrent(pageDO.getCurrent());
            pageResult.setRecords(Lists.newArrayList());
            return pageResult;
        }

        List<StoreListQueryResult> resultList = new ArrayList<>();
        List<MerchantStoreInfoDO> records = pageDO.getRecords();
        for (MerchantStoreInfoDO item : records) {
            StoreListQueryResult result = storeManagerMapper.coverMerchantStoreInfoDOToStoreListQueryResult(item);
            resultList.add(result);
        }
        PageResult<StoreListQueryResult> pageResult = new PageResult<>();
        pageResult.setTotal(pageDO.getTotal());
        pageResult.setSize(pageDO.getSize());
        pageResult.setCurrent(pageDO.getCurrent());
        pageResult.setRecords(resultList);
        return pageResult;
    }

    @Override
    public StoreInfoAddResult addStoreInfo(StoreInfoAddParam param) {

        ValidateUtil.validate(param);
        // 参数校验
        MerchantStoreInfoDO storeName = merchantStoreInfoDAO.getInfoByStoreName(param.getStoreName());
        if (null != storeName) {
            throw ValidateUtil.validateMsg("门店名称已存在");
        }
        MerchantDO merchantInfo = merchantDAO.getInfoByMerchantIdWithTenantId(param.getMerchantId());
        if (null == merchantInfo) {
            throw ValidateUtil.validateMsg("商户信息不存在");
        }
        if (StrUtil.equalsIgnoreCase("SUCCESS", merchantInfo.getMerchantStatus())) {
            throw ValidateUtil.validateMsg("商户状态异常");
        }
        UnityCategoryDO unityCategoryDO = unityCategoryDAO.getInfoById(param.getUnityCatId());
        if (null == unityCategoryDO) {
            throw ValidateUtil.validateMsg("类目信息不存在");
        }
        GaodeCodeDO provinceCode = gaodeCodeDAO.getInfoByCode(param.getProvinceCode());
        if (null == provinceCode) {
            throw ValidateUtil.validateMsg("省信息不存在");
        }
        GaodeCodeDO cityCode = gaodeCodeDAO.getInfoByCode(param.getCityCode());
        if (null == cityCode) {
            throw ValidateUtil.validateMsg("市信息不存在");
        }
        GaodeCodeDO countyCode = gaodeCodeDAO.getInfoByCode(param.getCountyCode());
        if (null == countyCode) {
            throw ValidateUtil.validateMsg("县信息不存在");
        }

        MerchantStoreInfoDO storeInfoDO = storeManagerMapper.coverStoreInfoAddParamToStoreInfoDO(param);
        storeInfoDO.setTenantId(TenantContextUtil.getTenantId());
        // todo xujw
        storeInfoDO.setStoreId(IdWorkerUtil.getSingleId());
        merchantStoreInfoDAO.save(storeInfoDO);
        StoreInfoAddResult result = new StoreInfoAddResult();
        result.setSuccess(true);
        return result;
    }

    @Override
    public StoreInfoUpdateResult updateStoreInfo(StoreInfoUpdateParam param) {

        // 参数校验
        MerchantStoreInfoDO storeName = merchantStoreInfoDAO.getInfoByStoreName(param.getStoreName());
        if (null != storeName) {
            throw ValidateUtil.validateMsg("门店名称已存在");
        }
        MerchantDO merchantInfo = merchantDAO.getInfoByMerchantIdWithTenantId(param.getMerchantId());
        if (null == merchantInfo) {
            throw ValidateUtil.validateMsg("商户信息不存在");
        }
        if (StrUtil.equalsIgnoreCase("SUCCESS", merchantInfo.getMerchantStatus())) {
            throw ValidateUtil.validateMsg("商户状态异常");
        }
        UnityCategoryDO unityCategoryDO = unityCategoryDAO.getInfoById(param.getUnityCatId());
        if (null == unityCategoryDO) {
            throw ValidateUtil.validateMsg("类目信息不存在");
        }
        GaodeCodeDO provinceCode = gaodeCodeDAO.getInfoByCode(param.getProvinceCode());
        if (null == provinceCode) {
            throw ValidateUtil.validateMsg("省信息不存在");
        }
        GaodeCodeDO cityCode = gaodeCodeDAO.getInfoByCode(param.getCityCode());
        if (null == cityCode) {
            throw ValidateUtil.validateMsg("市信息不存在");
        }
        GaodeCodeDO countyCode = gaodeCodeDAO.getInfoByCode(param.getCountyCode());
        if (null == countyCode) {
            throw ValidateUtil.validateMsg("县信息不存在");
        }
        MerchantStoreInfoDO dbStoreInfoDO = merchantStoreInfoDAO.getInfoByStoreIdWithTenantId(param.getStoreId());
        if (null == dbStoreInfoDO) {
            throw ValidateUtil.validateMsg("门店信息不存在");
        }

        MerchantStoreInfoDO storeInfoDO = storeManagerMapper.coverStoreInfoUpdateParamToStoreInfoDO(param);
        merchantStoreInfoDAO.updateByStoreIdAndMerchantIdWithTenantId(storeInfoDO);
        StoreInfoUpdateResult result = new StoreInfoUpdateResult();
        result.setSuccess(true);
        return result;
    }

    @Override
    public StoreInfoQueryResult queryStoreInfo(StoreInfoQueryParam param) {

        ValidateUtil.validate(param);

        MerchantStoreInfoDO infoDO = merchantStoreInfoDAO.getInfoByStoreIdWithTenantId(param.getStoreId());
        if (infoDO == null) {
            StoreInfoQueryResult result = new StoreInfoQueryResult();
            return result;
        }

        StoreInfoQueryResult result = storeManagerMapper.coverStoreInfoUpdateParamToStoreInfoQueryResult(infoDO);
        Integer unityCatId = result.getUnityCatId();
        result.setLevel1CategoryId(0);
        result.setLevel1CategoryName("未知");
        result.setUnityCatId(0);
        result.setUnityCatName("未知");
        if (unityCatId != null) {
            UnityCategoryDO secondCategoryDO = unityCategoryDAO.getInfoById(unityCatId);
            if (null != secondCategoryDO) {
                UnityCategoryDO firstCategoryDO = unityCategoryDAO.getInfoById(secondCategoryDO.getParentId());
                if (null != firstCategoryDO) {
                    result.setLevel1CategoryId(firstCategoryDO.getId());
                    result.setLevel1CategoryName(firstCategoryDO.getCatName());
                    result.setUnityCatId(secondCategoryDO.getId());
                    result.setUnityCatName(secondCategoryDO.getCatName());
                }
            }
        }
        return result;
    }
}
