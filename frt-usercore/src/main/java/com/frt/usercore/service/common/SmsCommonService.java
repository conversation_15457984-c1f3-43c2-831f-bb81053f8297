package com.frt.usercore.service.common;

import com.frt.usercore.domain.param.common.SmsMessageTemplateParam;
import com.frt.usercore.domain.result.common.SmsCodeCheckResult;

public interface SmsCommonService {

	/**
	 * 发送短信
	 * @param phone 手机号
	 * @param sceneValue 场景值
	 * @param tenantId 租户ID
	 * @param param 短信模板参数
	 * @return 发送结果
	 */
	Boolean sendSms(String phone, String sceneValue, String tenantId, SmsMessageTemplateParam param);

	/**
	 * 校验短信验证码
	 * @param phone 手机号
	 * @param code 验证码
	 * @param sceneValue 场景值
	 * @param tenantId 租户ID
	 * @return 校验结果
	 */
	SmsCodeCheckResult checkSmsCode(String phone, String code, String sceneValue, String tenantId);
}
