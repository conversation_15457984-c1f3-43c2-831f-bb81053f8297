package com.frt.usercore.service.common;

import com.frt.usercore.domain.param.common.SmsMessageTemplateParam;

public interface SmsCommonService {

	/**
	 * 发送短信
	 * @param phone 手机号
	 * @param sceneValue 场景值
	 * @param tenantId 租户ID
	 */
	Boolean sendSms(String phone, String sceneValue, String tenantId, SmsMessageTemplateParam param);

	/**
	 * 校验短信验证码
	 * @param phone 手机号
	 * @param code 验证码
	 * @param sceneValue 场景值
	 * @param tenantId 租户ID
	 */
	Boolean checkSmsCode(String phone, String code, String sceneValue, String tenantId);
}
