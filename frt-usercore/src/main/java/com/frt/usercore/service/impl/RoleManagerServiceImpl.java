/*
 * ailike.com
 * Copyright (C) 2022-2025 All Rights Reserved.
 */
package com.frt.usercore.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.db.Page;
import com.frt.usercore.common.enums.business.BusinessIdEnum;
import com.frt.usercore.common.enums.business.PlatformEnum;
import com.frt.usercore.common.enums.business.RoleTypeEnum;
import com.frt.usercore.common.utils.IdWorkerUtil;
import com.frt.usercore.common.utils.TenantContextUtil;
import com.frt.usercore.common.utils.ValidateUtil;
import com.frt.usercore.dao.entity.*;
import com.frt.usercore.dao.repository.*;
import com.frt.usercore.domain.mapper.MerchantAdminRoleManagerServiceObjMapper;
import com.frt.usercore.domain.param.PageParam;
import com.frt.usercore.domain.param.rolemanager.*;
import com.frt.usercore.domain.result.PageResult;
import com.frt.usercore.domain.result.common.CommonResult;
import com.frt.usercore.domain.result.rolemanager.RoleDetailQueryResult;
import com.frt.usercore.domain.result.rolemanager.RoleInfoResult;
import com.frt.usercore.service.RoleManagerService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version RoleManagerServiceImpl.java, v 0.1 2025-08-27 16:35 zhangling
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RoleManagerServiceImpl implements RoleManagerService {

    private final TenantRoleDAO tenantRoleDAO;

    private final RoleMenuDAO roleMenuDAO;

    private final BasicRoleTemplateDAO basicRoleTemplateDAO;

    private final TenantRolePermissionDAO tenantRolePermissionDAO;

    private final AccountBindRoleDAO accountBindRoleDAO;

    private final TransactionTemplate transactionTemplate;

    private final MerchantAdminRoleManagerServiceObjMapper mapper;

    /**
     * 退款权限值
     */
    private static final String REFUND_PERMISSION_VALUE = "REFUND_PERMISSION_VALUE";

    /**
     * 查询角色列表
     *
     * @param param 请求参数
     * @return 角色列表
     */
    @Override
    public PageResult<RoleInfoResult> getRoleList(PageParam<RoleListQueryParam> param) {
        return null;
    }

    /**
     * 查询角色详情
     *
     * @param param 请求参数
     * @return 角色详情
     */
    @Override
    public RoleDetailQueryResult getRoleDetail(RoleDetailQueryParam param) {
        ValidateUtil.validate(param);
        final PlatformEnum platformEnum = PlatformEnum.getByCode(param.getPlatformType());
        if (platformEnum == null) {
            throw ValidateUtil.validateMsg("平台类型不存在");
        }
        Integer roleType;
        if (PlatformEnum.MERCHANT == platformEnum) {
            roleType = RoleTypeEnum.MERCHANT.getCode();
        } else if (PlatformEnum.AGENT == platformEnum) {
            roleType = RoleTypeEnum.AGENT.getCode();
        } else {
            roleType = RoleTypeEnum.OPERATION.getCode();
        }
        String tenantId = TenantContextUtil.getTenantId();
        // 根据角色ID查询角色信息
        TenantRoleDO tenantRoleDO = tenantRoleDAO.getByTenantIdAndRoleIdAndRoleType(tenantId, param.getRoleId(), roleType);
        if (tenantRoleDO == null) {
            throw ValidateUtil.validateMsg("角色不存在");
        }

        // 查询角色关联的菜单ID列表
        final List<String> menuIdList = roleMenuDAO.getMenuIdListByTenantIdAndRoleId(tenantId, param.getRoleId());

        // 构建返回结果
        RoleDetailQueryResult result = new RoleDetailQueryResult();
        result.setRoleId(tenantRoleDO.getRoleId());
        result.setRoleName(tenantRoleDO.getRoleName());
        result.setRoleType(tenantRoleDO.getRoleType());
        result.setMenuIdList(CollectionUtil.isNotEmpty(menuIdList) ? menuIdList : new ArrayList<>());

        // 格式化创建时间
        if (tenantRoleDO.getCreateTime() != null) {
            result.setCreateTime(DateUtil.formatDateTime(tenantRoleDO.getCreateTime()));
        }

        // 查询员工退款权限
        final List<BasicPermissionDO> permissionList = tenantRolePermissionDAO.selectTenantRolePermissionByTenantIdAndRoleId(tenantId, tenantRoleDO.getRoleId());
        if (CollectionUtil.isEmpty(permissionList)) {
            result.setIsRefund(0);
        } else {
            result.setIsRefund(permissionList.stream().anyMatch(permission -> REFUND_PERMISSION_VALUE.equals(permission.getPermissionValue())) ? 1 : 0);
        }
        return result;
    }

    /**
     * 添加角色
     *
     * @param param 请求参数
     * @return 操作结果
     */
    @Override
    public CommonResult addRole(RoleAddParam param) {
        String tenantId = TenantContextUtil.getTenantId();
        TenantRoleDO tenantRoleDO = tenantRoleDAO.getByRoleName(param.getRoleName(), tenantId, param.getTerminalType(), null);
        if (null != tenantRoleDO) {
            throw ValidateUtil.validateMsg("该角色名称已重复，请修改后重新提交");
        }

        TenantRoleDO insertDO = new TenantRoleDO();
        insertDO.setRoleId(BusinessIdEnum.ROLE_ID.generateId());
        insertDO.setTenantId(tenantId);
        insertDO.setRoleName(param.getRoleName());
        insertDO.setRoleDescription(param.getRemark());
        insertDO.setRoleType(RoleTypeEnum.MERCHANT.getCode());
        List<String> menuIdList = param.getPermissionValueList();
        List<RoleMenuDO> roleMenuDOList = new ArrayList<>();
        for (String menuId : menuIdList) {
            RoleMenuDO roleMenuDO = new RoleMenuDO();
            roleMenuDO.setRoleId(insertDO.getRoleId());
            roleMenuDO.setTenantId(tenantId);
            roleMenuDO.setMenuId(menuId);
            roleMenuDOList.add(roleMenuDO);
        }
        // 事物
        transactionTemplate.execute(status -> {
            tenantRoleDAO.save(insertDO);
            roleMenuDAO.saveBatch(roleMenuDOList);
            return Boolean.TRUE;
        });
        return CommonResult.success();
    }

    /**
     * 更新角色
     *
     * @param param 请求参数
     * @return 操作结果
     */
    @Override
    public CommonResult updateRole(RoleUpdateParam param) {
        String tenantId = TenantContextUtil.getTenantId();
        // 根据角色ID查询角色信息
        TenantRoleDO tenantRoleDO = tenantRoleDAO.getByRoleId(param.getRoleId());
        if (tenantRoleDO == null) {
            throw ValidateUtil.validateMsg("角色不存在");
        }
        // 校验角色名称是否重复（排除当前角色）
        TenantRoleDO duplicateRole = tenantRoleDAO.getByRoleName(param.getRoleName(), tenantId, param.getTerminalType(), param.getRoleId());
        if (duplicateRole != null) {
            throw ValidateUtil.validateMsg("该角色名称已重复，请修改后重新提交");
        }

        // 更新角色信息
        TenantRoleDO updateDO = new TenantRoleDO();
        updateDO.setId(tenantRoleDO.getId());
        updateDO.setRoleName(param.getRoleName());
        updateDO.setRoleDescription(param.getRemark());

        // 准备新的菜单权限列表
        List<String> menuIdList = param.getPermissionValueList();
        List<RoleMenuDO> roleMenuDOList = new ArrayList<>();
        for (String menuId : menuIdList) {
            RoleMenuDO roleMenuDO = new RoleMenuDO();
            roleMenuDO.setRoleId(param.getRoleId());
            roleMenuDO.setTenantId(tenantId);
            roleMenuDO.setMenuId(menuId);
            roleMenuDOList.add(roleMenuDO);
        }
        // 修改退款权限
        boolean refund = this.checkRefundPermission(tenantId, tenantRoleDO.getRoleId());
        // 是否需要添加/移除退款权限;refundOperateType = 1：添加权限; refundOperateType = 2：移除权限; refundOperateType = 3：不处理
        int refundOperateType;
        if (param.getIsRefund() == 1 && !refund) {
            refundOperateType = 1;
        } else if (param.getIsRefund() == 0 && refund) {
            refundOperateType = 2;
        } else {
            refundOperateType = 3;
        }
        // 事务处理
        transactionTemplate.execute(status -> {
            // 更新角色基本信息
            tenantRoleDAO.updateById(updateDO);

            // 删除原有的角色菜单关联
            roleMenuDAO.removeByRoleId(param.getRoleId());

            // 新增角色菜单关联
            roleMenuDAO.saveBatch(roleMenuDOList);
            // 添加退款权限
            if (refundOperateType == 1) {
                final TenantRolePermissionDO rolePermissionDO = new TenantRolePermissionDO();
                rolePermissionDO.setRoleId(tenantRoleDO.getRoleId());
                rolePermissionDO.setTenantId(tenantId);
                rolePermissionDO.setPermissionId(REFUND_PERMISSION_VALUE);
                tenantRolePermissionDAO.save(rolePermissionDO);
            } else if (refundOperateType == 2) {
                // 移除退款权限
                tenantRolePermissionDAO.removeByTenantIdAndRoleId(tenantId, tenantRoleDO.getRoleId());
            }
            return Boolean.TRUE;
        });
        return CommonResult.success();
    }

    private boolean checkRefundPermission(String tenantId, String roleId) {
        final List<BasicPermissionDO> permissionList = tenantRolePermissionDAO.selectTenantRolePermissionByTenantIdAndRoleId(tenantId, roleId);
        if (CollectionUtil.isEmpty(permissionList)) {
            return false;
        }
        return permissionList.stream().anyMatch(permission -> REFUND_PERMISSION_VALUE.equals(permission.getPermissionValue()));
    }

    /**
     * 删除角色
     *
     * @param param 请求参数
     * @return 操作结果
     */
    @Override
    public CommonResult deleteRole(RoleDeleteParam param) {
        String tenantId = TenantContextUtil.getTenantId();
        // 根据角色ID查询角色信息
        TenantRoleDO tenantRoleDO = tenantRoleDAO.getByRoleId(param.getRoleId());
        if (tenantRoleDO == null) {
            throw ValidateUtil.validateMsg("角色不存在");
        }
        // 若角色绑定员工，则不允许删除
        final AccountBindRoleDO bindRoleDO = accountBindRoleDAO.getOneByRoleId(tenantRoleDO.getRoleId());
        if (bindRoleDO != null) {
            throw ValidateUtil.validateMsg("该角色已绑定员工，请先解除绑定");
        }
        // 事物
        transactionTemplate.execute(status -> {
            tenantRoleDAO.removeByRoleId(tenantRoleDO.getRoleId());
            roleMenuDAO.removeByRoleId(tenantRoleDO.getRoleId());
            tenantRolePermissionDAO.removeByTenantIdAndRoleId(tenantId, tenantRoleDO.getRoleId());
            return Boolean.TRUE;
        });
        return CommonResult.success();
    }
}