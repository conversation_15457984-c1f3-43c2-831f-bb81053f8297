/**
 * fshows.com
 * Copyright (C) 2013-2025 All Rights Reserved.
 */
package com.frt.usercore.config;

import com.frt.usercore.common.utils.LogUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Web配置类
 * <p>
 * 配置Web相关的组件，包括拦截器、跨域设置等
 * </p>
 *
 * <AUTHOR>
 * @version WebConfig.java, v 0.1 2025-08-28 11:00 wangyi
 */
@Slf4j
@Configuration
@RequiredArgsConstructor
public class WebConfig implements WebMvcConfigurer {

    /**
     * 租户拦截器
     */
    private final TenantInterceptor tenantInterceptor;

    /**
     * 添加拦截器配置
     * <p>
     * 注册租户拦截器，配置拦截路径和排除路径
     * </p>
     *
     * @param registry 拦截器注册器
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        LogUtil.info(log, "WebConfig.addInterceptors >> 开始注册拦截器");
        
        // 注册租户拦截器
        registry.addInterceptor(tenantInterceptor)
                // 拦截所有请求
                .addPathPatterns("/**")
                // 排除不需要租户上下文的路径
                .excludePathPatterns(
                        // 错误页面
                        "/error"
                )
                // 设置拦截器执行顺序（数值越小优先级越高）
                .order(1);
        
        LogUtil.info(log, "WebConfig.addInterceptors >> 租户拦截器注册完成");
        
        // 调用父类方法，确保其他配置正常生效
        WebMvcConfigurer.super.addInterceptors(registry);
        
        LogUtil.info(log, "WebConfig.addInterceptors >> 拦截器注册完成");
    }
}
