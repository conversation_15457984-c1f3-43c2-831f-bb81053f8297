/**
 * fshows.com
 * Copyright (C) 2013-2025 All Rights Reserved.
 */
package com.frt.usercore.config;

import cn.hutool.core.util.StrUtil;
import com.frt.usercore.common.constants.base.BaseConstants;
import com.frt.usercore.common.utils.LogUtil;
import com.frt.usercore.common.utils.TenantContextUtil;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

/**
 * 租户拦截器
 * <p>
 * 拦截所有HTTP请求，从请求头中获取租户ID并存储到线程上下文中，
 * 确保在整个请求处理过程中都能获取到正确的租户信息
 * </p>
 *
 * <AUTHOR>
 * @version TenantInterceptor.java, v 0.1 2025-08-28 10:30 wangyi
 */
@Slf4j
@Component
public class TenantInterceptor implements HandlerInterceptor {

    /**
     * 请求处理前的拦截方法
     * <p>
     * 从HTTP请求头中获取租户ID，并将其存储到线程上下文中
     * </p>
     *
     * @param request  HTTP请求对象
     * @param response HTTP响应对象
     * @param handler  处理器对象
     * @return 是否继续处理请求，true表示继续，false表示中断
     * @throws Exception 处理过程中可能抛出的异常
     */
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) 
            throws Exception {
        
        String requestUri = request.getRequestURI();
        String method = request.getMethod();
        
        LogUtil.info(log, "TenantInterceptor.preHandle >> 开始处理请求 >> method = {}, uri = {}", 
                method, requestUri);
        
        try {
            // 先清理可能存在的旧上下文（防止线程复用导致的数据混乱）
            TenantContextUtil.clear();

            // 从请求头中获取租户ID
            String tenantId = request.getHeader(BaseConstants.TENANT_ID_HEADER);

            if (StrUtil.isNotBlank(tenantId)) {
                // 设置租户上下文
                TenantContextUtil.setTenantId(tenantId);
                LogUtil.info(log, "TenantInterceptor.preHandle >> 租户上下文设置成功 >> tenantId = {}", tenantId);
            }
            
            return true;

        } catch (Exception e) {
            LogUtil.error(log, "TenantInterceptor.preHandle >> 处理租户上下文时发生异常 >> error = {}",
                    e.getMessage(), e);

            // 异常情况下清理上下文
            TenantContextUtil.clear();

            // 继续处理请求，不因为租户上下文设置失败而中断业务流程
            return true;
        }
    }

    /**
     * 请求处理完成后的清理方法
     * <p>
     * 清理线程上下文中的租户信息，防止内存泄漏
     * </p>
     *
     * @param request   HTTP请求对象
     * @param response  HTTP响应对象
     * @param handler   处理器对象
     * @param exception 处理过程中的异常（如果有）
     * @throws Exception 清理过程中可能抛出的异常
     */
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, 
                               Object handler, Exception exception) throws Exception {
        
        String requestUri = request.getRequestURI();
        String tenantId = TenantContextUtil.getTenantId();
        
        LogUtil.info(log, "TenantInterceptor.afterCompletion >> 开始清理租户上下文 >> " +
                "uri = {}, tenantId = {}", requestUri, tenantId);
        
        try {
            // 清理租户上下文
            TenantContextUtil.clear();
            
            LogUtil.info(log, "TenantInterceptor.afterCompletion >> 租户上下文清理完成 >> uri = {}", 
                    requestUri);
            
        } catch (Exception e) {
            LogUtil.error(log, "TenantInterceptor.afterCompletion >> 清理租户上下文时发生异常 >> " +
                    "uri = {}, error = {}", requestUri, e.getMessage(), e);
        }
    }


}
