/**
 * fshows.com
 * Copyright (C) 2013-2025 All Rights Reserved.
 */
package com.frt.generalgw.config;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

/**
 * <AUTHOR>
 * @version FeignConfig.java, v 0.1 2025-08-27 09:38 wangyi
 */
@Configuration
public class FeignConfig implements RequestInterceptor {

    /**
     * 发送feign请求前的header处理
     *
     * @param requestTemplate
     */
    @Override
    public void apply(RequestTemplate requestTemplate) {
        HttpServletRequest request = getServletRequest();
        if (null == request) {
            return;
        }
        // todo 租户信息
        requestTemplate.header("TENANT_ID", "123");
    }

    private HttpServletRequest getServletRequest() {
        return ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
    }
}