package com.frt.generalgw.client.usercore.operationadmin;

import com.frt.generalgw.domain.param.operationadmin.menumanager.MenuListQueryParam;
import com.frt.generalgw.domain.result.operationadmin.menumanager.MenuListQueryResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 运营后台菜单管理客户端
 *
 * <AUTHOR>
 * @version OperationAdminMenuManagerClient.java, v 0.1 2025-08-28 15:00 tuyuwei
 */
@FeignClient(name = "usercore", contextId = "operationAdminMenuManagerClient")
public interface OperationAdminMenuManagerClient {

    /**
     * 权限列表
     *
     * @param param 请求参数
     * @return 权限列表
     */
    @PostMapping("/api/operation/admin/menu/menu-list")
    List<MenuListQueryResult> getMenuList(@RequestBody MenuListQueryParam param);
}
