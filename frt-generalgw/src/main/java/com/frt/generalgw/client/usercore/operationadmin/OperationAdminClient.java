package com.frt.generalgw.client.usercore.operationadmin;

import com.frt.generalgw.domain.param.operationadmin.auth.*;
import com.frt.generalgw.domain.result.operationadmin.auth.OperationAdminLoginResult;
import com.frt.generalgw.domain.result.operationadmin.auth.OperationAdminResourceResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 运营后台权限客户端
 */
@FeignClient(name = "usercore", contextId = "operationAdminAuthClient")
public interface OperationAdminClient {

    /**
     * 4.1 登录页资源获取接口
     * 接口名称：operation/web/search/resource
     * 请求方式：POST
     *
     * @param param 请求参数 webAddress 二级域名
     * @return 资源信息
     */
    @PostMapping("/operation/web/search/resource")
    OperationAdminResourceResult searchResource(@RequestBody OperationAdminResourceParam param);

    /**
     * 4.2 发送验证码
     * 接口名称：operation/web/send/code
     * 请求方式：POST
     *
     * @param param 请求参数
     *              tenantId 租户id
     *              phone 手机号
     *              type 场景类型 1-登录 2-修改密码
     */
    @PostMapping("/operation/web/send/code")
    void sendCode(@RequestBody OperationAdminSendCodeParam param);

    /**
     * 4.3 账号登录
     * 接口名称：operation/web/login
     * 请求方式：POST
     *
     * @param param 登录参数
     *              webAddress 二级域名
     *              tenantId 租户Id
     *              type 登录方式 1-密码登录 2-验证码登录
     *              account 账号
     *              password 密码（md5加密）
     *              code 验证码
     * @return 登录结果
     */
    @PostMapping("/operation/web/login")
    OperationAdminLoginResult login(@RequestBody OperationAdminLoginParam param);





    /**
     * 4.7 账号登出
     * 接口名称：operation/web/logout
     * 请求方式：POST
     */
    @PostMapping("/operation/web/logout")
    void logout();
}