/**
 * fshows.com
 * Copyright (C) 2013-2025 All Rights Reserved.
 */
package com.frt.generalgw.client.usercore.merchantmina;

import com.frt.generalgw.config.FeignConfig;
import com.frt.generalgw.domain.param.merchantmina.common.CommonAddressCodeListQueryParam;
import com.frt.generalgw.domain.param.merchantmina.common.CommonUnityCategoryListQueryParam;
import com.frt.generalgw.domain.result.merchantmina.common.CommonAddressCodeListQueryResult;
import com.frt.generalgw.domain.result.merchantmina.common.CommonUnityCategoryListQueryResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(
        contextId = "merchant-mina-common-client",
        value = "frt-usercore-dev",
        configuration = {FeignConfig.class}
)
public interface MerchantMinaCommonClient {

    /**
     * 查询地址列表
     *
     * @return
     */
    @PostMapping("/api/common/query/address-code-list")
    CommonAddressCodeListQueryResult queryAddressCodeList(@RequestBody CommonAddressCodeListQueryParam param);

    /**
     * 查询类目列表
     * @param param
     * @return
     */
    @PostMapping("/api/common/query/unity-category-list")
    CommonUnityCategoryListQueryResult queryUnityCategoryList(@RequestBody CommonUnityCategoryListQueryParam param);
}