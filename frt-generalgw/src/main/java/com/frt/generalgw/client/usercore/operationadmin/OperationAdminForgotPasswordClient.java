package com.frt.generalgw.client.usercore.operationadmin;

import com.frt.generalgw.domain.param.operationadmin.forgotpassword.CheckSmsCodeParam;
import com.frt.generalgw.domain.param.operationadmin.forgotpassword.CheckVerifyCodeParam;
import com.frt.generalgw.domain.param.operationadmin.forgotpassword.GetVerifyCodeParam;
import com.frt.generalgw.domain.param.operationadmin.forgotpassword.SendSmsParam;
import com.frt.generalgw.domain.param.operationadmin.forgotpassword.UpdatePasswordParam;
import com.frt.generalgw.domain.result.operationadmin.forgotpassword.CheckVerifyCodeResult;
import com.frt.generalgw.domain.result.operationadmin.forgotpassword.GetVerifyCodeResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 运营后台忘记密码客户端
 *
 * <AUTHOR>
 * @version OperationAdminForgotPasswordClient.java, v 0.1 2025-08-28 15:00 tuyuwei
 */
@FeignClient(name = "usercore", contextId = "operationAdminForgotPasswordClient")
public interface OperationAdminForgotPasswordClient {

    /**
     * 获取图形验证码
     *
     * @param param 请求参数
     * @return 验证码图片URL
     */
    @PostMapping("/api/operation/admin/forgot-password/get-verify-code")
    GetVerifyCodeResult getVerifyCode(@RequestBody GetVerifyCodeParam param);

    /**
     * 校验图文验证码
     *
     * @param param 请求参数
     * @return 校验结果
     */
    @PostMapping("/api/operation/admin/forgot-password/check-verify-code")
    CheckVerifyCodeResult checkVerifyCode(@RequestBody CheckVerifyCodeParam param);

    /**
     * 发送短信
     *
     * @param param 请求参数
     */
    @PostMapping("/api/operation/admin/forgot-password/send-sms")
    void sendSms(@RequestBody SendSmsParam param);

    /**
     * 校验验证码
     *
     * @param param 请求参数
     */
    @PostMapping("/api/operation/admin/forgot-password/check-sms-code")
    void checkSmsCode(@RequestBody CheckSmsCodeParam param);

    /**
     * 修改密码
     *
     * @param param 请求参数
     */
    @PostMapping("/api/operation/admin/forgot-password/update-password")
    void updatePassword(@RequestBody UpdatePasswordParam param);
}
