/**
 * fshows.com
 * Copyright (C) 2013-2025 All Rights Reserved.
 */
package com.frt.generalgw.client.usercore.test;

import com.frt.generalgw.config.FeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * <AUTHOR>
 * @version TestClient.java, v 0.1 2025-08-26 20:07 wangyi
 */
@FeignClient(
        contextId = "merchant-admin-test-client",
        value = "frt-usercore-dev",
        configuration = {FeignConfig.class}
)
public interface TestClient {

    /**
     * 测试请求
     *
     * @return
     */
    @GetMapping("/api/test/hello")
    String hello();
}