/*
 * ailike.com
 * Copyright (C) 2022-2025 All Rights Reserved.
 */
package com.frt.generalgw.client.usercore.protocol;

import com.frt.generalgw.config.FeignConfig;
import com.frt.generalgw.domain.param.ProtocolListQueryParam;
import com.frt.generalgw.domain.param.ProtocolQueryParam;
import com.frt.generalgw.domain.result.ProtocolInfoResult;
import com.frt.generalgw.domain.result.ProtocolListQueryResult;
import com.frt.generalgw.domain.result.ProtocolSignCheckResult;
import com.frt.generalgw.domain.result.common.ListResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * <AUTHOR>
 * @version ProtocolClient.java, v 0.1 2025-08-27 13:59 zhangling
 */
@FeignClient(value = "frt-usercore-dev", configuration = {FeignConfig.class})
public interface ProtocolClient {

    /**
     * 查询协议签署列表
     *
     * @return
     */
    @GetMapping("/api/protocol/query/protocol-list")
    ProtocolSignCheckResult findProtocolList(ProtocolListQueryParam param);

    /**
     * 查询协议签署内容
     *
     * @return
     */
    @GetMapping("/api/protocol/get/protocol-info")
    ProtocolInfoResult getProtocolInfo(ProtocolQueryParam param);
}