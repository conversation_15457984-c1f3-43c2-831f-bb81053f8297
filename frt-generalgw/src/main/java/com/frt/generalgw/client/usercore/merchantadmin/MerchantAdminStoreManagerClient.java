/**
 * fshows.com
 * Copyright (C) 2013-2025 All Rights Reserved.
 */
package com.frt.generalgw.client.usercore.merchantadmin;

import com.frt.generalgw.config.FeignConfig;
import com.frt.generalgw.domain.param.common.PageParam;
import com.frt.generalgw.domain.param.merchantadmin.storemanager.MerchantAminStoreInfoAddParam;
import com.frt.generalgw.domain.param.merchantadmin.storemanager.MerchantAminStoreInfoQueryParam;
import com.frt.generalgw.domain.param.merchantadmin.storemanager.MerchantAminStoreInfoUpdateParam;
import com.frt.generalgw.domain.param.merchantadmin.storemanager.MerchantAminStoreListQueryParam;
import com.frt.generalgw.domain.result.common.PageResult;
import com.frt.generalgw.domain.result.merchantadmin.storemanager.MerchantAminStoreInfoAddResult;
import com.frt.generalgw.domain.result.merchantadmin.storemanager.MerchantAminStoreInfoQueryResult;
import com.frt.generalgw.domain.result.merchantadmin.storemanager.MerchantAminStoreInfoUpdateResult;
import com.frt.generalgw.domain.result.merchantadmin.storemanager.MerchantAminStoreListQueryResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(
        contextId = "merchant-admin-store-manager-client",
        value = "frt-usercore-dev",
        configuration = {FeignConfig.class}
)
public interface MerchantAdminStoreManagerClient {

    /**
     * 查询门店列表
     * @param param
     * @return
     */
    @PostMapping("/api/store/query/list")
    PageResult<MerchantAminStoreListQueryResult> queryStoreList(@RequestBody PageParam<MerchantAminStoreListQueryParam> param);

    /**
     * 添加门店信息
     * @param param
     * @return
     */
    @PostMapping("/api/store/add/info")
    MerchantAminStoreInfoAddResult addStoreInfo(@RequestBody MerchantAminStoreInfoAddParam param);

    /**
     * 修改门店信息
     * @param param
     * @return
     */
    @PostMapping("/api/store/update/info")
    MerchantAminStoreInfoUpdateResult updateStoreInfo(@RequestBody MerchantAminStoreInfoUpdateParam param);

    /**
     * 查询门店信息
     * @param param
     * @return
     */
    @PostMapping("/api/store/query/info")
    MerchantAminStoreInfoQueryResult queryStoreInfo(@RequestBody MerchantAminStoreInfoQueryParam param);
}