/**
 * <AUTHOR>
 * @date 2025/8/27 14:43
 * @version 1.0 MerchantMinaController
 */
package com.frt.generalgw.controller.merchantmina;

/**
 *
 *
 * <AUTHOR>
 * @version MerchantMinaController.java, v 0.1 2025-08-27 14:43 tuyuwei
 */



import com.frt.generalgw.client.usercore.merchantmina.MerchantMinaClient;
import com.frt.generalgw.domain.param.merchantmina.auth.*;
import com.frt.generalgw.domain.param.merchantmina.forgotpassword.CheckSmsCodeParam;
import com.frt.generalgw.domain.param.merchantmina.forgotpassword.CheckVerifyCodeParam;
import com.frt.generalgw.domain.param.merchantmina.forgotpassword.GetVerifyCodeParam;
import com.frt.generalgw.domain.param.merchantmina.forgotpassword.SendSmsParam;
import com.frt.generalgw.domain.param.merchantmina.forgotpassword.UpdatePasswordParam;
import com.frt.generalgw.domain.result.CommonResult;
import com.frt.generalgw.domain.result.merchantmina.auth.MerchantMinaLoginResult;
import com.frt.generalgw.domain.result.merchantmina.auth.MerchantMinaResourceResult;
import com.frt.generalgw.domain.result.merchantmina.auth.MerchantMinaSearchPhoneResult;
import com.frt.generalgw.domain.result.merchantmina.forgotpassword.CheckVerifyCodeResult;
import com.frt.generalgw.domain.result.merchantmina.forgotpassword.GetVerifyCodeResult;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 商家版小程序/MerchantMinaAuthController
 */
@RestController
@RequestMapping("/merchant/mina/auth")
@Slf4j
public class MerchantMinaAuthController {

    @Autowired
    private MerchantMinaClient merchantMinaClient;

    @PostMapping("/search/resource")
    public MerchantMinaResourceResult searchResource(@RequestBody MerchantMinaResourceParam param) {
        return merchantMinaClient.searchResource(param);
    }

    @PostMapping("/send/code")
    public CommonResult sendCode(@RequestBody MerchantMinaSendCodeParam param) {
        merchantMinaClient.sendCode(param);
        return CommonResult.success();
    }

    @PostMapping("/login")
    public MerchantMinaLoginResult login(@RequestBody MerchantMinaLoginParam param) {
        return merchantMinaClient.login(param);
    }

    @PostMapping("/search/phone")
    public MerchantMinaSearchPhoneResult searchPhone(@RequestBody MerchantMinaSearchPhoneParam param) {
        return merchantMinaClient.searchPhone(param);
    }

    @PostMapping("/check/code")
    public CommonResult checkCode(@RequestBody MerchantMinaCheckCodeParam param) {
        merchantMinaClient.checkCode(param);
        return CommonResult.success();
    }

    @PostMapping("/change/password")
    public CommonResult changePassword(@RequestBody MerchantMinaChangePasswordParam param) {
        merchantMinaClient.changePassword(param);
        return CommonResult.success();
    }

    @PostMapping("/logout")
    public CommonResult logout() {
        merchantMinaClient.logout();
        return CommonResult.success();
    }

    // ========== 忘记密码相关接口 ==========

    /**
     * 获取图形验证码
     *
     * @param param 请求参数
     * @return 验证码图片URL
     */
    @PostMapping("/get-verify-code")
    public GetVerifyCodeResult getVerifyCode(@Validated @RequestBody GetVerifyCodeParam param) {
        // TODO: 实现获取图形验证码逻辑
        return null;
    }

    /**
     * 校验图文验证码
     *
     * @param param 请求参数
     * @return 校验结果
     */
    @PostMapping("/check-verify-code")
    public CheckVerifyCodeResult checkVerifyCode(@Validated @RequestBody CheckVerifyCodeParam param) {
        // TODO: 实现校验图文验证码逻辑
        return null;
    }

    /**
     * 发送短信
     *
     * @param param 请求参数
     */
    @PostMapping("/sen-sms")
    public void sendSms(@Validated @RequestBody SendSmsParam param) {
        // TODO: 实现发送短信逻辑
    }

    /**
     * 校验验证码
     *
     * @param param 请求参数
     */
    @PostMapping("/check-sms-code")
    public void checkSmsCode(@Validated @RequestBody CheckSmsCodeParam param) {
        // TODO: 实现校验短信验证码逻辑
    }

    /**
     * 修改密码
     *
     * @param param 请求参数
     */
    @PostMapping("/update-password")
    public void updatePassword(@Validated @RequestBody UpdatePasswordParam param) {
        // TODO: 实现修改密码逻辑
    }

}