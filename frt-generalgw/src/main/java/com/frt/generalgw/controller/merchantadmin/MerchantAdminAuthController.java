/**
 * <AUTHOR>
 * @date 2025/8/27 14:16
 * @version 1.0 MerchantAuthController
 */
package com.frt.generalgw.controller.merchantadmin;

/**
 *
 *
 * <AUTHOR>
 * @version MerchantAuthController.java, v 0.1 2025-08-27 14:16 tuyuwei
 */

import com.frt.generalgw.client.usercore.merchantadmin.MerchantAdminClient;
import com.frt.generalgw.domain.param.merchantadmin.auth.*;
import com.frt.generalgw.domain.param.merchantadmin.forgotpassword.CheckSmsCodeParam;
import com.frt.generalgw.domain.param.merchantadmin.forgotpassword.CheckVerifyCodeParam;
import com.frt.generalgw.domain.param.merchantadmin.forgotpassword.GetVerifyCodeParam;
import com.frt.generalgw.domain.param.merchantadmin.forgotpassword.SendSmsParam;
import com.frt.generalgw.domain.result.CommonResult;
import com.frt.generalgw.domain.result.merchantadmin.auth.MerchantAdminLoginResult;
import com.frt.generalgw.domain.result.merchantadmin.auth.MerchantAdminResourceResult;
import com.frt.generalgw.domain.result.merchantadmin.auth.MerchantAdminSearchPhoneResult;
import com.frt.generalgw.domain.result.merchantadmin.forgotpassword.CheckVerifyCodeResult;
import com.frt.generalgw.domain.result.merchantadmin.forgotpassword.GetVerifyCodeResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 商家版后台/MerchantAdminAuthController
 */
@RestController
@RequestMapping("/merchant/web")
public class MerchantAdminAuthController {

    @Autowired
    private MerchantAdminClient merchantAdminClient;

    /**
     * 3.1.1 登录页资源获取接口
     *
     * @param param 请求参数
     * @return 资源信息
     */
    @PostMapping("/search/resource")
    public CommonResult<MerchantAdminResourceResult> searchResource(@RequestBody MerchantAdminResourceParam param) {
        MerchantAdminResourceResult result = merchantAdminClient.searchResource(param);
        return CommonResult.success(result);
    }

    /**
     * 3.1.2 发送验证码
     *
     * @param param 请求参数
     * @return 发送结果
     */
    @PostMapping("/send/code")
    public CommonResult<Void> sendCode(@RequestBody MerchantAdminSendCodeParam param) {
        merchantAdminClient.sendCode(param);
        return CommonResult.success();
    }

    /**
     * 3.1.3 账号登录
     *
     * @param param 登录参数
     * @return 登录结果
     */
    @PostMapping("/login")
    public CommonResult<MerchantAdminLoginResult> login(@RequestBody MerchantAdminLoginParam param) {
        MerchantAdminLoginResult result = merchantAdminClient.login(param);
        return CommonResult.success(result);
    }

    /**
     * 3.1.4 通过账号查询加密手机号
     *
     * @param param 查询参数
     * @return 手机号信息
     */
    @PostMapping("/search/phone")
    public CommonResult<MerchantAdminSearchPhoneResult> searchPhone(@RequestBody MerchantAdminSearchPhoneParam param) {
        MerchantAdminSearchPhoneResult result = merchantAdminClient.searchPhone(param);
        return CommonResult.success(result);
    }

    /**
     * 3.1.5 修改密码验证码校验
     *
     * @param param 验证参数
     * @return 校验结果
     */
    @PostMapping("/check/code")
    public CommonResult<Void> checkCode(@RequestBody MerchantAdminCheckCodeParam param) {
        merchantAdminClient.checkCode(param);
        return CommonResult.success();
    }

    /**
     * 3.1.6 设置新密码
     *
     * @param param 修改密码参数
     * @return 修改结果
     */
    @PostMapping("/change/password")
    public CommonResult<Void> changePassword(@RequestBody MerchantAdminChangePasswordParam param) {
        merchantAdminClient.changePassword(param);
        return CommonResult.success();
    }

    /**
     * 3.1.7 账号登出
     *
     * @return 登出结果
     */
    @PostMapping("/logout")
    public CommonResult<Void> logout() {
        merchantAdminClient.logout();
        return CommonResult.success();
    }

    /**
     * 获取图形验证码
     *
     * @param param 请求参数
     * @return 验证码图片URL
     */
    @PostMapping("/get-verify-code")
    public GetVerifyCodeResult getVerifyCode(@Validated @RequestBody GetVerifyCodeParam param) {
        // TODO: 实现获取图形验证码逻辑
        return new GetVerifyCodeResult();
    }

    /**
     * 校验图文验证码
     *
     * @param param 请求参数
     * @return 校验结果
     */
    @PostMapping("/check-verify-code")
    public CheckVerifyCodeResult checkVerifyCode(@Validated @RequestBody CheckVerifyCodeParam param) {
        return new CheckVerifyCodeResult();
    }

    /**
     * 发送短信
     *
     * @param param 请求参数
     */
    @PostMapping("/sen-sms")
    public void sendSms(@Validated @RequestBody SendSmsParam param) {
        // TODO: 实现发送短信逻辑
    }

    /**
     * 校验验证码
     *
     * @param param 请求参数
     */
    @PostMapping("/check-sms-code")
    public void checkSmsCode(@Validated @RequestBody CheckSmsCodeParam param) {
        // TODO: 实现校验短信验证码逻辑
    }
}