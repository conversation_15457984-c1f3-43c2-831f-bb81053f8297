/*
 * ailike.com
 * Copyright (C) 2022-2025 All Rights Reserved.
 */
package com.frt.generalgw.controller.merchantmina;

import com.frt.generalgw.client.usercore.merchantadmin.UserManagerClient;
import com.frt.generalgw.domain.entity.UserInfo;
import com.frt.generalgw.domain.param.*;
import com.frt.generalgw.domain.param.common.PageParam;
import com.frt.generalgw.domain.result.CommonResult;
import com.frt.generalgw.domain.result.UserDetailQueryResult;
import com.frt.generalgw.domain.result.UserListQueryResult;
import com.frt.generalgw.domain.result.common.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 商家版小程序/MerchantMinaUserManagerController
 *
 * <AUTHOR>
 * @version MerchantMinaUserManagerController.java, v 0.1 2025-08-27 16:51 zhangling
 */
@RestController
@RequestMapping("/merchantmina/user")
public class MerchantMinaUserManagerController {

    @Autowired
    private UserManagerClient userManagerClient;

    /**
     * 查询员工列表
     *
     * @param param 查询参数
     * @return 员工列表结果
     */
    @PostMapping("/query-user-list")
    public PageResult<UserInfo> queryUserList(@RequestBody PageParam<UserListQueryParam> param) {
        return userManagerClient.getUserList(param);
    }

    /**
     * 查询员工详情
     *
     * @param param 查询参数
     * @return 员工详情结果
     */
    @PostMapping("/get-user-detail")
    public UserDetailQueryResult getUserDetail(@RequestBody UserDetailQueryParam param) {
        return userManagerClient.getUserDetail(param);
    }

    /**
     * 添加员工
     *
     * @param param 添加参数
     * @return 添加结果
     */
    @PostMapping("/add-user")
    public CommonResult addUser(@RequestBody UserAddParam param) {
        return userManagerClient.addUser(param);
    }

    /**
     * 更新员工
     *
     * @param param 更新参数
     * @return 更新结果
     */
    @PostMapping("/update-user")
    public CommonResult updateUser(@RequestBody UserUpdateParam param) {
        return userManagerClient.updateUser(param);
    }

    /**
     * 删除员工
     *
     * @param param 删除参数
     * @return 删除结果
     */
    @PostMapping("/delete-user")
    public CommonResult deleteUser(@RequestBody UserDeleteParam param) {
        return userManagerClient.deleteUser(param);
    }
}