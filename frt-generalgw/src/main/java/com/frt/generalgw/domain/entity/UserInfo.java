/*
 * ailike.com
 * Copyright (C) 2022-2025 All Rights Reserved.
 */
package com.frt.generalgw.domain.entity;

import lombok.Data;

import java.io.Serializable;

/**
 * 员工信息实体类
 *
 * <AUTHOR>
 * @version UserInfo.java, v 0.1 2025-08-27 16:51 zhangling
 */
@Data
public class UserInfo implements Serializable {

    private static final long serialVersionUID = -871271297905358376L;

    /**
     * 员工ID
     */
    private String userId;

    /**
     * 员工账号
     */
    private String account;

    /**
     * 员工姓名
     */
    private String name;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 角色ID
     */
    private String roleId;

    /**
     * 角色名称
     */
    private String roleName;
    /**
     * 所属门店
     */
    private String storeName;

    /**
     * 状态 0-禁用 1-启用
     */
    private Integer status;

    /**
     * 备注
     */
    private String remark;
    /**
     * 最近登录时间
     */
    private String lastLoginTime;

    /**
     * 创建时间
     */
    private String createTime;
}