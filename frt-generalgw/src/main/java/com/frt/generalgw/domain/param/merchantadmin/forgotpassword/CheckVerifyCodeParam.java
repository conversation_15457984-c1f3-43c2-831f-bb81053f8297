package com.frt.generalgw.domain.param.merchantadmin.forgotpassword;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 校验图文验证码参数
 *
 * <AUTHOR>
 * @version CheckVerifyCodeParam.java, v 0.1 2025-08-27 15:00 tuyuwei
 */
@Data
public class CheckVerifyCodeParam implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 账号
     */
    @NotBlank(message = "账号不能为空")
    private String account;

    /**
     * 图文验证码
     */
    @NotBlank(message = "图文验证码不能为空")
    private String code;

    /**
     * 平台类型 1-租户 2-代理商 3-商户（查询账号使用）
     */
    @NotNull(message = "平台类型不能为空")
    private Integer platformType;
}
