/*
 * ailike.com
 * Copyright (C) 2022-2025 All Rights Reserved.
 */
package com.frt.generalgw.domain.param;

import lombok.Data;

import java.io.Serializable;

/**
 * 员工列表查询参数
 *
 * <AUTHOR>
 * @version UserListQueryParam.java, v 0.1 2025-08-27 16:51 zhangling
 */
@Data
public class UserListQueryParam implements Serializable {

    private static final long serialVersionUID = -123456789012345678L;

    /**
     * 员工账号
     */
    private String account;

    /**
     * 员工姓名
     */
    private String name;

    /**
     * 模糊查询字段
     */
    private String keyword;

    /**
     * 角色类型 1-收银员 2-店长
     */
    private Integer roleType;

    /**
     * 状态 0-禁用 1-启用
     */
    private Integer status;

    /**
     * 门店ID
     */
    private Integer storeId;
}