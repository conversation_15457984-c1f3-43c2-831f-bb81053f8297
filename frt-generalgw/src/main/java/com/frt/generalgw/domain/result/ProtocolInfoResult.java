/*
 * ailike.com
 * Copyright (C) 2022-2025 All Rights Reserved.
 */
package com.frt.generalgw.domain.result;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version ProtocolInfoResult.java, v 0.1 2025-08-27 13:55 zhangling
 */
@Data
public class ProtocolInfoResult implements Serializable {
    private static final long serialVersionUID = 8851440805926708821L;
    /**
     * 协议ID
     */
    private String protocolId;

    /**
     * 协议名称
     */
    private String protocolName;

    /**
     * 协议内容
     */
    private String protocolContent;
}