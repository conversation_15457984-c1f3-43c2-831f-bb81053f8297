package com.frt.generalgw.domain.param.merchantadmin.forgotpassword;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 校验验证码参数
 *
 * <AUTHOR>
 * @version CheckSmsCodeParam.java, v 0.1 2025-08-27 15:00 tuyuwei
 */
@Data
public class CheckSmsCodeParam implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 账号ID
     */
    @NotBlank(message = "账号ID不能为空")
    private String account_id;

    /**
     * 平台类型 1-租户 2-代理商 3-商户
     */
    @NotNull(message = "平台类型不能为空")
    private Integer platformType;

    /**
     * 短信验证码
     */
    @NotBlank(message = "短信验证码不能为空")
    private String code;

    /**
     * 场景值（忘记密码，验证码登录）
     */
    @NotBlank(message = "场景值不能为空")
    private String sceneValue;

    /**
     * 租户ID
     */
    @NotBlank(message = "租户ID不能为空")
    private String tenantId;

    /**
     * MD5密码
     */
    @NotBlank(message = "密码不能为空")
    private String password;
}
