package com.frt.generalgw.domain.result;

import com.frt.generalgw.domain.entity.RoleInfo;
import lombok.Data;

import java.io.Serializable;

/**
 * 角色详情查询结果
 *
 * <AUTHOR>
 * @version RoleDetailQueryResult.java, v 0.1 2025-08-27 14:52 zhangling
 */
@Data
public class RoleDetailQueryResult implements Serializable {

    private static final long serialVersionUID = -4423990568731270630L;

    /**
     * 角色信息
     */
    private RoleInfo roleInfo;
}