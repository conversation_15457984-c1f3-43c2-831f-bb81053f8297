package com.frt.generalgw.domain.param.merchantmina.storemanager;

import lombok.Data;

@Data
public class MerchantMinaStoreInfoAddParam {
    /**
     * 商户ID
     */
    private String merchantId;
    /**
     * 门店名称
     */
    private String storeName;
    /**
     * 省
     */
    private String provinceName;
    /**
     * 省code
     */
    private String provinceCode;
    /**
     * 市
     */
    private String cityName;
    /**
     * 市code
     */
    private String cityCode;
    /**
     * 区
     */
    private String countyName;
    /**
     * 区code
     */
    private String countyCode;
    /**
     * 地址
     */
    private String storeAddress;
    /**
     * 门店电话
     */
    private String storePhone;
    /**
     * 行业类目
     */
    private Integer unityCatId;
    /**
     * 门头照
     */
    private String storeDoorPic;
    /**
     * 收银台
     */
    private String storeCashierPic;
    /**
     * 店内环境
     */
    private String storeEnvironmentPic;

}
